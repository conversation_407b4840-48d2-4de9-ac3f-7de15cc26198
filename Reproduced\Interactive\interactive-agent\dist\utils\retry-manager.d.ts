/**
 * Configuration for retry behavior
 */
export interface RetryPolicy {
    maxRetries: number;
    baseDelayMs: number;
    maxDelayMs: number;
    backoffFactor: number;
    retryableErrors: readonly string[];
}
/**
 * Error types that should trigger retries
 */
export declare const RETRYABLE_ERROR_CODES: {
    readonly ENOENT: "ENOENT";
    readonly EACCES: "EACCES";
    readonly EMFILE: "EMFILE";
    readonly ENFILE: "ENFILE";
    readonly EAGAIN: "EAGAIN";
    readonly SPAWN_FAILED: "SPAWN_FAILED";
    readonly SPAWN_TIMEOUT: "SPAWN_TIMEOUT";
    readonly HEARTBEAT_TIMEOUT: "HEARTBEAT_TIMEOUT";
    readonly EBUSY: "EBUSY";
    readonly ENOTEMPTY: "ENOTEMPTY";
};
/**
 * Pre-configured retry policies for common scenarios
 */
export declare const RETRY_POLICIES: {
    readonly SPAWN_RETRY_POLICY: {
        readonly maxRetries: 3;
        readonly baseDelayMs: 1000;
        readonly maxDelayMs: 10000;
        readonly backoffFactor: 2;
        readonly retryableErrors: readonly ["ENOENT", "EACCES", "EMFILE", "ENFILE", "EAGAIN", "SPAWN_FAILED", "SPAWN_TIMEOUT"];
    };
    readonly FILE_OPERATION_RETRY_POLICY: {
        readonly maxRetries: 2;
        readonly baseDelayMs: 500;
        readonly maxDelayMs: 5000;
        readonly backoffFactor: 1.5;
        readonly retryableErrors: readonly ["EBUSY", "ENOTEMPTY", "EAGAIN"];
    };
    readonly NETWORK_RETRY_POLICY: {
        readonly maxRetries: 5;
        readonly baseDelayMs: 2000;
        readonly maxDelayMs: 30000;
        readonly backoffFactor: 2;
        readonly retryableErrors: readonly ["ECONNRESET", "ECONNREFUSED", "ETIMEDOUT", "ENOTFOUND"];
    };
};
/**
 * Information about retry attempts
 */
export interface RetryAttemptInfo {
    attempt: number;
    totalAttempts: number;
    delay: number;
    error: Error;
    isLastAttempt: boolean;
}
/**
 * Result of a retry operation
 */
export interface RetryResult<T> {
    success: boolean;
    result?: T;
    error?: Error;
    attempts: number;
    totalDelay: number;
    errors: Error[];
}
/**
 * Intelligent retry system for handling failures with exponential backoff
 */
export declare class RetryManager {
    /**
     * Calculate delay for a given attempt with jitter
     */
    static calculateDelay(attempt: number, policy: RetryPolicy): number;
    /**
     * Check if an error should trigger a retry
     */
    static isRetryableError(error: Error, policy: RetryPolicy): boolean;
    /**
     * Execute an operation with retry logic
     */
    static executeWithRetry<T>(operation: (abortSignal?: AbortSignal) => Promise<T>, policy: RetryPolicy, abortSignal?: AbortSignal): Promise<RetryResult<T>>;
    /**
     * Delay with abort signal support
     */
    private static delay;
    /**
     * Create a retry policy with custom overrides
     */
    static createPolicy(basePolicy: RetryPolicy, overrides: Partial<RetryPolicy>): RetryPolicy;
    /**
     * Wrap a function to automatically apply retry logic
     */
    static withRetry<T extends any[], R>(fn: (...args: T) => Promise<R>, policy: RetryPolicy): (...args: T) => Promise<R>;
}
//# sourceMappingURL=retry-manager.d.ts.map