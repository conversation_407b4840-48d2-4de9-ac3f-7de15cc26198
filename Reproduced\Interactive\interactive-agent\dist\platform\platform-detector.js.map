{"version": 3, "file": "platform-detector.js", "sourceRoot": "", "sources": ["../../src/platform/platform-detector.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AAGzB,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,qBAAqB,EAAE,MAAM,iBAAiB,CAAC;AAmBxD,qBAAqB;AACrB,IAAI,kBAAkB,GAAwB,IAAI,CAAC;AACnD,IAAI,cAAc,GAAW,CAAC,CAAC;AAE/B;;GAEG;AACH,SAAS,SAAS;IAChB,iDAAiD;IACjD,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC;QACtD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,2DAA2D;IAC3D,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,EAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;YAC7D,OAAO,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC;gBAC/C,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACnD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,oDAAoD;YACpD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,4DAA4D;gBAC5D,gEAAgE;gBAChE,OAAO,KAAK,CAAC;YACf,CAAC;iBAAM,CAAC;gBACN,iEAAiE;gBACjE,OAAO,CAAC,IAAI,CACV,iDAAiD,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,IAAI;oBACpG,0FAA0F,CAC3F,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,UAAU;IACjB,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAS,WAAW;IAClB,UAAU;IACV,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC;IAC1C,CAAC;IAED,oBAAoB;IACpB,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;QACtB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;IAC3B,CAAC;IAED,wCAAwC;IACxC,MAAM,YAAY,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IACvE,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,yDAAyD;YACzD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,oDAAoD;gBACpD,SAAS;YACX,CAAC;iBAAM,CAAC;gBACN,gDAAgD;gBAChD,OAAO,CAAC,IAAI,CACV,oCAAoC,KAAK,MAAM,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,IAAI;oBAClG,sCAAsC,CACvC,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC,CAAC,kBAAkB;AACtC,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB;IAC1B,8BAA8B;IAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,qDAAqD;IACrD,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,uDAAuD;IACvD,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oBAAoB;IACpB,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB;IAC5B,2CAA2C;IAC3C,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;IACjG,IAAI,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mBAAmB;IACnB,IAAI,CAAC;QACH,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,uDAAuD;QACvD,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5B,gDAAgD;YAChD,OAAO,CAAC,IAAI,CACV,mDAAmD,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,IAAI;gBACtG,sCAAsC,CACvC,CAAC;QACJ,CAAC;QACD,sEAAsE;IACxE,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAID;;GAEG;AACH,KAAK,UAAU,wBAAwB;IACrC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,IAAI,kBAAkB,GAAa,EAAE,CAAC;IAEtC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,oBAAoB;QACpB,kBAAkB,GAAG;YACnB,QAAQ,EAAE,mBAAmB;YAC7B,cAAc;YACd,YAAY;YACZ,SAAS;YACT,gBAAgB;SACjB,CAAC;IACJ,CAAC;SAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,wEAAwE;QACxE,kBAAkB,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,6CAA6C;QAE5E,oDAAoD;QACpD,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,qBAAqB;QACrB,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,yBAAyB,CAAC,EAAE,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CACV,uEAAuE,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,EAAE,CACzH,CAAC;YACJ,CAAC;QACH,CAAC;QAED,wBAAwB;QACxB,IAAI,CAAC;YACH,IAAI,EAAE,CAAC,UAAU,CAAC,sCAAsC,CAAC,EAAE,CAAC;gBAC1D,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAC/B,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CACV,oFAAoF,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,EAAE,CACtI,CAAC;YACJ,CAAC;QACH,CAAC;QAED,qEAAqE;QACrE,IAAI,MAAM,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7E,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,EAAE,CAAC;IACZ,CAAC;SAAM,CAAC;QACN,kBAAkB;QAClB,kBAAkB,GAAG;YACnB,gBAAgB;YAChB,SAAS;YACT,gBAAgB;YAChB,eAAe;YACf,YAAY;YACZ,OAAO;YACP,OAAO;SACR,CAAC;IACJ,CAAC;IAED,6CAA6C;IAC7C,6EAA6E;IAC7E,MAAM,oBAAoB,GAAG,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QACrE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,iBAAiB,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YACvE,OAAO,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yCAAyC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC;IAC/D,OAAO,OAAO;SACX,MAAM,CAAC,CAAC,MAAM,EAA4C,EAAE,CAC3D,MAAM,CAAC,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CACvD;SACA,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,YAAmC;IAC3D,yCAAyC;IACzC,IAAI,YAAY,CAAC,UAAU,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,uDAAuD;IACvD,IAAI,CAAC,YAAY,CAAC,UAAU,IAAI,EAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,EAAE,CAAC;QAC1D,OAAO,KAAK,CAAC;IACf,CAAC;IAED,6CAA6C;IAC7C,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CAAC,eAAwB,KAAK;IACtE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEvB,4CAA4C;IAC5C,IAAI,CAAC,YAAY,IAAI,kBAAkB,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,qBAAqB,EAAE,CAAC;QAC1F,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,0CAA0C;IAC1C,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC;IAC1B,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;IAC5B,MAAM,KAAK,GAAG,WAAW,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,mBAAmB,EAAE,CAAC;IACzC,MAAM,UAAU,GAAG,qBAAqB,EAAE,CAAC;IAC3C,MAAM,kBAAkB,GAAG,MAAM,wBAAwB,EAAE,CAAC;IAE5D,MAAM,YAAY,GAAiB;QACjC,QAAQ;QACR,KAAK;QACL,MAAM;QACN,KAAK;QACL,UAAU;QACV,UAAU;QACV,kBAAkB;QAClB,gBAAgB,EAAE,KAAK,CAAC,yBAAyB;KAClD,CAAC;IAEF,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAE/D,kBAAkB;IAClB,kBAAkB,GAAG,YAAY,CAAC;IAClC,cAAc,GAAG,GAAG,CAAC;IAErB,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,eAAwB,KAAK;IAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAEvB,4CAA4C;IAC5C,IAAI,CAAC,YAAY,IAAI,kBAAkB,IAAI,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,qBAAqB,EAAE,CAAC;QAC1F,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,6EAA6E;IAC7E,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC;IAC1B,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;IAC5B,MAAM,KAAK,GAAG,WAAW,EAAE,CAAC;IAC5B,MAAM,UAAU,GAAG,mBAAmB,EAAE,CAAC;IACzC,MAAM,UAAU,GAAG,qBAAqB,EAAE,CAAC;IAE3C,+DAA+D;IAC/D,IAAI,kBAAkB,GAAa,EAAE,CAAC;IACtC,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QACzB,kBAAkB,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,mCAAmC;IACzF,CAAC;SAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACjC,6CAA6C;QAC7C,IAAI,CAAC;YACH,kBAAkB,GAAG,EAAE,CAAC,UAAU,CAAC,sCAAsC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrG,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CACV,2FAA2F,KAAK,CAAC,IAAI,IAAI,eAAe,KAAK,KAAK,CAAC,OAAO,EAAE,CAC7I,CAAC;YACJ,CAAC;YACD,kBAAkB,GAAG,EAAE,CAAC,CAAC,oBAAoB;QAC/C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,qDAAqD;QACrD,kBAAkB,GAAG,CAAC,OAAO,CAAC,CAAC;IACjC,CAAC;IAED,MAAM,YAAY,GAAiB;QACjC,QAAQ;QACR,KAAK;QACL,MAAM;QACN,KAAK;QACL,UAAU;QACV,UAAU;QACV,kBAAkB;QAClB,gBAAgB,EAAE,KAAK,CAAC,yBAAyB;KAClD,CAAC;IAEF,YAAY,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,YAAY,CAAC,CAAC;IAE/D,kDAAkD;IAClD,oDAAoD;IAEpD,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,yBAAyB;IAC7C,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB;IAClC,OAAO,eAAe,CAAC,IAAI,CAAC,CAAC;AAC/B,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,4BAA4B;IAChD,MAAM,YAAY,GAAG,MAAM,oBAAoB,EAAE,CAAC;IAElD,OAAO;QACL,QAAQ,EAAE,YAAY,CAAC,gBAAgB;QACvC,iBAAiB,EAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC7D,iBAAiB,EAAE,YAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC5D,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,uBAAuB;IACrC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,OAAO;QACL,QAAQ,EAAE,YAAY,CAAC,gBAAgB;QACvC,iBAAiB,EAAE,YAAY,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,IAAI;QAC7D,iBAAiB,EAAE,YAAY,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;KAC5D,CAAC;AACJ,CAAC"}