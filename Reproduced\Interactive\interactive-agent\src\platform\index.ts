// Re-exports from platform-detector
export {
  PlatformInfo,
  TerminalCapabilities,
  getPlatformInfo,
  getPlatformInfoAsync,
  refreshPlatformCache,
  refreshPlatformCacheAsync,
  getTerminalCapabilities,
  getTerminalCapabilitiesAsync
} from './platform-detector.js';

// Re-exports from process-spawn
export {
  SpawnOptions,
  SpawnResult,
  spawnInTerminal,
  spawnWithRetry,
  testTerminalSpawn
} from './process-spawn.js';

// Re-exports from terminal-fallbacks
export {
  TerminalInfo,
  EnvironmentInfo,
  TerminalFallbacks
} from './terminal-fallbacks.js';

// Re-exports from retry-manager
export {
  RetryPolicy
} from '../utils/retry-manager.js';

// Convenience functions and initialization
import { getPlatformInfo } from './platform-detector.js';
import { spawnInTerminal, spawnWithRetry, SpawnResult } from './process-spawn.js';
import { TerminalFallbacks, TerminalInfo } from './terminal-fallbacks.js';
import { orphanManager } from '../utils/orphan-manager.js';
import { PlatformType } from '../constants.js';

/**
 * Initialize platform module with orphan manager and signal handlers
 */
export function initializePlatform(): void {
  // Orphan manager is automatically initialized as singleton
  console.debug('Platform module initialized with orphan tracking');
}

/**
 * Check if terminal spawning is possible in current environment
 */
export async function canSpawnTerminal(): Promise<boolean> {
  return await TerminalFallbacks.canSpawnTerminal();
}

/**
 * Find the best terminal for current environment
 */
export async function findBestTerminal(): Promise<TerminalInfo | null> {
  return await TerminalFallbacks.findBestTerminal();
}

/**
 * Get available terminals for current platform
 */
export async function getAvailableTerminals(): Promise<TerminalInfo[]> {
  return await TerminalFallbacks.getAvailableTerminals();
}

/**
 * Detect preferred shell for current platform
 */
export function detectShell(): string {
  const platformInfo = getPlatformInfo();
  return platformInfo.shell;
}



/**
 * Enhanced spawn with automatic fallback and retry logic
 */
export async function spawnWithFallback(
  command: string,
  args: string[] = [],
  options: import('./process-spawn.js').SpawnOptions = {}
): Promise<SpawnResult & { usedFallback: boolean }> {
  // Try enhanced spawn with retry first
  const result = await spawnWithRetry(command, args, options);

  if (result.success) {
    return { ...result, usedFallback: false };
  }

  // Check fallback strategy
  const fallbackStrategy = TerminalFallbacks.getFallbackStrategy();

  if (fallbackStrategy === 'console') {
    return {
      process: null,
      success: false,
      error: 'Terminal spawning failed, console fallback recommended',
      usedFallback: true
    };
  }

  // Try to find alternative terminals
  const availableTerminals = await TerminalFallbacks.getAvailableTerminals();
  const workingTerminals = availableTerminals.filter(t => t.available);

  for (const terminal of workingTerminals) {
    try {
      const fallbackResult = spawnInTerminal(command, args, options);
      if (fallbackResult.success) {
        return { ...fallbackResult, usedFallback: true };
      }
    } catch (error) {
      continue;
    }
  }

  return {
    process: null,
    success: false,
    error: 'All terminal spawn attempts failed',
    usedFallback: true
  };
}



/**
 * Obtient le type de plateforme simplifié
 */
export function getPlatformType(): PlatformType {
  const platformInfo = getPlatformInfo();
  
  switch (platformInfo.platform) {
    case 'win32':
      return 'win32';
    case 'darwin':
      return 'darwin';
    case 'linux':
      return 'linux';
    default:
      return 'other';
  }
}

/**
 * Vérifie si la plateforme est Windows (incluant WSL)
 */
export function isWindows(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'win32' || platformInfo.isWSL;
}

/**
 * Vérifie si la plateforme est macOS
 */
export function isMacOS(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'darwin';
}

/**
 * Vérifie si la plateforme est Linux (excluant WSL)
 */
export function isLinux(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.platform === 'linux' && !platformInfo.isWSL;
}

/**
 * Vérifie si nous sommes dans WSL
 */
export function isWSL(): boolean {
  const platformInfo = getPlatformInfo();
  return platformInfo.isWSL;
}

/**
 * Obtient un résumé des capacités de la plateforme
 */
export interface PlatformSummary {
  type: PlatformType;
  canSpawnTerminal: boolean;
  hasDisplay: boolean;
  isHeadless: boolean;
  preferredShell: string;
  terminalCount: number;
}

export function getPlatformSummary(): PlatformSummary {
  const platformInfo = getPlatformInfo();

  return {
    type: getPlatformType(),
    canSpawnTerminal: platformInfo.canSpawnTerminal,
    hasDisplay: platformInfo.hasDisplay,
    isHeadless: platformInfo.isHeadless,
    preferredShell: platformInfo.shell,
    terminalCount: platformInfo.availableTerminals.length
  };
}

// Auto-initialize platform module when imported
initializePlatform();
