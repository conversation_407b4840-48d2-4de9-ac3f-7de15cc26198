/**
 * Enhanced input handling utility
 * Supports both console-based input and platform-specific terminal spawning
 */
import { RetryPolicy } from './retry-manager.js';
export interface UserInputOptions {
    projectName: string;
    message: string;
    predefinedOptions?: string[];
    useTerminalIfAvailable?: boolean;
    retryPolicy?: RetryPolicy;
    abortSignal?: AbortSignal;
}
export interface UserInputResponse {
    response: string;
    timestamp: number;
    usedTerminal?: boolean;
    sessionId?: string;
    retryCount?: number;
}
/**
 * Enhanced getUserInput function with terminal support
 */
export declare function getUserInput(projectName: string, message: string, predefinedOptions?: string[], useTerminalIfAvailable?: boolean, retryPolicy?: RetryPolicy, abortSignal?: AbortSignal): Promise<UserInputResponse>;
/**
 * Enhanced getUserInput function that accepts UserInputOptions
 */
export declare function getUserInputWithOptions(options: UserInputOptions): Promise<UserInputResponse>;
/**
 * Utility function to check if terminal input is available
 */
export declare function isTerminalInputAvailable(): Promise<boolean>;
/**
 * Get platform information for input handling
 */
export declare function getInputPlatformInfo(): {
    platform: NodeJS.Platform;
    canSpawnTerminal: boolean;
    hasDisplay: boolean;
    isHeadless: boolean;
    availableTerminals: string[];
};
//# sourceMappingURL=input.d.ts.map