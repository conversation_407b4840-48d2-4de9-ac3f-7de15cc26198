#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';
import { performance } from 'perf_hooks';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// ANSI color codes for output formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Configuration from command line arguments
const config = {
  interactive: process.argv.includes('--interactive'),
  verbose: process.argv.includes('--verbose'),
  quick: process.argv.includes('--quick'),
  json: process.argv.includes('--json'),
  help: process.argv.includes('--help') || process.argv.includes('-h'),
};

// Test results storage
const results = {
  platform: {},
  terminals: {},
  fileSystem: {},
  processes: {},
  performance: {},
  errors: [],
  warnings: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0,
  },
};

function log(message, color = 'reset') {
  if (!config.json) {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }
}

function logVerbose(message, color = 'reset') {
  if (config.verbose && !config.json) {
    console.log(`${colors[color]}  ${message}${colors.reset}`);
  }
}

function addResult(category, test, passed, details = '', warning = false) {
  results.summary.total++;
  if (passed) {
    results.summary.passed++;
  } else if (warning) {
    results.summary.warnings++;
    results.warnings.push({ category, test, details });
  } else {
    results.summary.failed++;
    results.errors.push({ category, test, details });
  }
  
  if (!results[category]) {
    results[category] = {};
  }
  results[category][test] = { passed, details, warning };
}

async function validateBuild() {
  log('\n🔧 Validating Build...', 'cyan');
  
  const distPath = join(projectRoot, 'dist');
  const indexPath = join(distPath, 'index.js');
  
  if (!existsSync(distPath)) {
    addResult('build', 'dist_directory', false, 'dist directory not found - run npm run build');
    return false;
  }
  
  if (!existsSync(indexPath)) {
    addResult('build', 'index_file', false, 'dist/index.js not found - run npm run build');
    return false;
  }
  
  addResult('build', 'dist_directory', true, 'dist directory exists');
  addResult('build', 'index_file', true, 'index.js exists');
  
  logVerbose('Build validation completed', 'green');
  return true;
}

async function validatePlatformDetection() {
  log('\n🖥️  Validating Platform Detection...', 'cyan');
  
  try {
    // Dynamic import to handle potential build issues
    const { getPlatformInfo } = await import(join(projectRoot, 'dist', 'platform', 'platform-detector.js'));
    
    const startTime = performance.now();
    const platformInfo = await getPlatformInfo();
    const endTime = performance.now();
    
    results.performance.platformDetection = endTime - startTime;
    
    // Validate platform info structure
    const requiredFields = ['platform', 'isWSL', 'isMSYS', 'shell', 'hasDisplay', 'isHeadless', 'terminals'];
    let allFieldsPresent = true;
    
    for (const field of requiredFields) {
      if (!(field in platformInfo)) {
        addResult('platform', `field_${field}`, false, `Missing required field: ${field}`);
        allFieldsPresent = false;
      } else {
        addResult('platform', `field_${field}`, true, `Field ${field} present`);
      }
    }
    
    // Validate platform value
    const validPlatforms = ['win32', 'darwin', 'linux'];
    if (validPlatforms.includes(platformInfo.platform)) {
      addResult('platform', 'platform_value', true, `Platform: ${platformInfo.platform}`);
    } else {
      addResult('platform', 'platform_value', false, `Invalid platform: ${platformInfo.platform}`);
    }
    
    // Validate boolean fields
    const booleanFields = ['isWSL', 'isMSYS', 'hasDisplay', 'isHeadless'];
    for (const field of booleanFields) {
      if (typeof platformInfo[field] === 'boolean') {
        addResult('platform', `type_${field}`, true, `${field} is boolean`);
      } else {
        addResult('platform', `type_${field}`, false, `${field} is not boolean: ${typeof platformInfo[field]}`);
      }
    }
    
    // Validate terminals array
    if (Array.isArray(platformInfo.terminals)) {
      addResult('platform', 'terminals_array', true, `Found ${platformInfo.terminals.length} terminals`);
      
      if (platformInfo.terminals.length > 0) {
        addResult('platform', 'terminals_available', true, `Terminals: ${platformInfo.terminals.join(', ')}`);
      } else {
        addResult('platform', 'terminals_available', false, 'No terminals detected', true);
      }
    } else {
      addResult('platform', 'terminals_array', false, 'terminals is not an array');
    }
    
    // Log platform info if verbose
    if (config.verbose) {
      logVerbose(`Platform: ${platformInfo.platform}`, 'blue');
      logVerbose(`WSL: ${platformInfo.isWSL}`, 'blue');
      logVerbose(`MSYS: ${platformInfo.isMSYS}`, 'blue');
      logVerbose(`Shell: ${platformInfo.shell}`, 'blue');
      logVerbose(`Display: ${platformInfo.hasDisplay}`, 'blue');
      logVerbose(`Headless: ${platformInfo.isHeadless}`, 'blue');
      logVerbose(`Terminals: ${platformInfo.terminals.join(', ')}`, 'blue');
      logVerbose(`Detection time: ${results.performance.platformDetection.toFixed(2)}ms`, 'blue');
    }
    
    return allFieldsPresent;
    
  } catch (error) {
    addResult('platform', 'detection_error', false, `Platform detection failed: ${error.message}`);
    return false;
  }
}

async function validateTerminalSpawning() {
  log('\n🖥️  Validating Terminal Spawning...', 'cyan');
  
  if (config.quick) {
    log('  Skipping terminal spawning tests in quick mode', 'yellow');
    return true;
  }
  
  try {
    const { spawnInTerminal } = await import(join(projectRoot, 'dist', 'platform', 'process-spawn.js'));
    
    // Test safe command that won't leave processes
    const testCommand = process.platform === 'win32' ? 'echo test' : 'echo test';
    
    if (config.interactive) {
      log('  Testing terminal spawn (this will open a terminal window)...', 'yellow');
      
      const startTime = performance.now();
      const result = await spawnInTerminal(testCommand, { timeout: 5000 });
      const endTime = performance.now();
      
      results.performance.terminalSpawn = endTime - startTime;
      
      if (result.success) {
        addResult('terminals', 'spawn_test', true, `Terminal spawn successful (${results.performance.terminalSpawn.toFixed(2)}ms)`);
      } else {
        addResult('terminals', 'spawn_test', false, `Terminal spawn failed: ${result.error}`);
      }
    } else {
      addResult('terminals', 'spawn_test', true, 'Skipped (non-interactive mode)', true);
    }
    
    return true;
    
  } catch (error) {
    addResult('terminals', 'spawn_error', false, `Terminal spawn validation failed: ${error.message}`);
    return false;
  }
}

async function validateFileSystem() {
  log('\n📁 Validating File System Operations...', 'cyan');
  
  try {
    const { createTempFile, createTempDir, cleanupTempFiles } = await import(join(projectRoot, 'dist', 'utils', 'temp-file-manager.js'));
    
    // Test temp file creation
    const startTime = performance.now();
    const tempFile = await createTempFile('test content', '.txt');
    const fileTime = performance.now();
    
    if (tempFile && existsSync(tempFile)) {
      addResult('fileSystem', 'temp_file_creation', true, `Created: ${tempFile}`);
    } else {
      addResult('fileSystem', 'temp_file_creation', false, 'Failed to create temp file');
    }
    
    // Test temp directory creation
    const tempDir = await createTempDir();
    const dirTime = performance.now();
    
    if (tempDir && existsSync(tempDir)) {
      addResult('fileSystem', 'temp_dir_creation', true, `Created: ${tempDir}`);
    } else {
      addResult('fileSystem', 'temp_dir_creation', false, 'Failed to create temp directory');
    }
    
    // Test cleanup
    await cleanupTempFiles();
    const cleanupTime = performance.now();
    
    addResult('fileSystem', 'cleanup', true, 'Cleanup completed');
    
    results.performance.fileOperations = {
      fileCreation: fileTime - startTime,
      dirCreation: dirTime - fileTime,
      cleanup: cleanupTime - dirTime,
    };
    
    logVerbose(`File creation: ${results.performance.fileOperations.fileCreation.toFixed(2)}ms`, 'blue');
    logVerbose(`Dir creation: ${results.performance.fileOperations.dirCreation.toFixed(2)}ms`, 'blue');
    logVerbose(`Cleanup: ${results.performance.fileOperations.cleanup.toFixed(2)}ms`, 'blue');
    
    return true;
    
  } catch (error) {
    addResult('fileSystem', 'operations_error', false, `File system validation failed: ${error.message}`);
    return false;
  }
}

async function validateRetryLogic() {
  log('\n🔄 Validating Retry Logic...', 'cyan');
  
  try {
    const { executeWithRetry, RetryPolicy } = await import(join(projectRoot, 'dist', 'utils', 'retry-manager.js'));
    
    // Test successful operation
    let callCount = 0;
    const successFn = async () => {
      callCount++;
      return 'success';
    };
    
    const startTime = performance.now();
    const result = await executeWithRetry(successFn);
    const successTime = performance.now();
    
    if (result === 'success' && callCount === 1) {
      addResult('retry', 'success_no_retry', true, 'Successful operation without retry');
    } else {
      addResult('retry', 'success_no_retry', false, `Unexpected behavior: result=${result}, calls=${callCount}`);
    }
    
    // Test retry on failure
    let retryCallCount = 0;
    const retryFn = async () => {
      retryCallCount++;
      if (retryCallCount < 3) {
        throw new Error('Temporary failure');
      }
      return 'success after retry';
    };
    
    const retryResult = await executeWithRetry(retryFn, { maxRetries: 3 });
    const retryTime = performance.now();
    
    if (retryResult === 'success after retry' && retryCallCount === 3) {
      addResult('retry', 'retry_success', true, `Success after ${retryCallCount} attempts`);
    } else {
      addResult('retry', 'retry_success', false, `Unexpected retry behavior: result=${retryResult}, calls=${retryCallCount}`);
    }
    
    results.performance.retryOperations = {
      success: successTime - startTime,
      retry: retryTime - successTime,
    };
    
    logVerbose(`Success operation: ${results.performance.retryOperations.success.toFixed(2)}ms`, 'blue');
    logVerbose(`Retry operation: ${results.performance.retryOperations.retry.toFixed(2)}ms`, 'blue');
    
    return true;
    
  } catch (error) {
    addResult('retry', 'logic_error', false, `Retry logic validation failed: ${error.message}`);
    return false;
  }
}

function printSummary() {
  if (config.json) {
    console.log(JSON.stringify(results, null, 2));
    return;
  }
  
  log('\n📊 Validation Summary', 'bright');
  log('═'.repeat(50), 'blue');
  
  log(`Total Tests: ${results.summary.total}`, 'blue');
  log(`Passed: ${results.summary.passed}`, 'green');
  log(`Failed: ${results.summary.failed}`, results.summary.failed > 0 ? 'red' : 'green');
  log(`Warnings: ${results.summary.warnings}`, results.summary.warnings > 0 ? 'yellow' : 'green');
  
  if (results.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    for (const error of results.errors) {
      log(`  ${error.category}.${error.test}: ${error.details}`, 'red');
    }
  }
  
  if (results.warnings.length > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    for (const warning of results.warnings) {
      log(`  ${warning.category}.${warning.test}: ${warning.details}`, 'yellow');
    }
  }
  
  // Performance summary
  if (Object.keys(results.performance).length > 0) {
    log('\n⚡ Performance:', 'magenta');
    if (results.performance.platformDetection) {
      log(`  Platform Detection: ${results.performance.platformDetection.toFixed(2)}ms`, 'magenta');
    }
    if (results.performance.terminalSpawn) {
      log(`  Terminal Spawn: ${results.performance.terminalSpawn.toFixed(2)}ms`, 'magenta');
    }
  }
  
  log('\n' + '═'.repeat(50), 'blue');
  
  const success = results.summary.failed === 0;
  log(`Validation ${success ? 'PASSED' : 'FAILED'}`, success ? 'green' : 'red');
  
  return success;
}

function printHelp() {
  console.log(`
Interactive Agent Platform Validator

Usage: node validate-platform.js [options]

Options:
  --interactive    Enable interactive tests (may open terminal windows)
  --verbose        Show detailed output
  --quick          Skip time-consuming tests
  --json           Output results in JSON format
  --help, -h       Show this help message

Examples:
  node validate-platform.js                    # Basic validation
  node validate-platform.js --verbose          # Detailed output
  node validate-platform.js --interactive      # Include terminal tests
  node validate-platform.js --quick --json     # Quick validation with JSON output
`);
}

async function main() {
  if (config.help) {
    printHelp();
    process.exit(0);
  }
  
  if (!config.json) {
    log('🚀 Interactive Agent Platform Validator', 'bright');
    log('═'.repeat(50), 'blue');
  }
  
  try {
    // Run validation steps
    const buildValid = await validateBuild();
    if (!buildValid) {
      log('\n❌ Build validation failed. Please run "npm run build" first.', 'red');
      process.exit(1);
    }
    
    await validatePlatformDetection();
    await validateTerminalSpawning();
    await validateFileSystem();
    await validateRetryLogic();
    
    // Print results
    const success = printSummary();
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    if (config.json) {
      console.error(JSON.stringify({ error: error.message }, null, 2));
    } else {
      log(`\n💥 Validation failed with error: ${error.message}`, 'red');
      if (config.verbose) {
        console.error(error.stack);
      }
    }
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the validator
main().catch(console.error);
