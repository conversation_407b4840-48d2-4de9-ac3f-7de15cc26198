{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/platform/index.ts"], "names": [], "mappings": "AAAA,oCAAoC;AACpC,OAAO,EAGL,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACpB,yBAAyB,EACzB,uBAAuB,EACvB,4BAA4B,EAC7B,MAAM,wBAAwB,CAAC;AAEhC,gCAAgC;AAChC,OAAO,EAGL,eAAe,EACf,cAAc,EACd,iBAAiB,EAClB,MAAM,oBAAoB,CAAC;AAE5B,qCAAqC;AACrC,OAAO,EAGL,iBAAiB,EAClB,MAAM,yBAAyB,CAAC;AAOjC,2CAA2C;AAC3C,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,eAAe,EAAE,cAAc,EAAe,MAAM,oBAAoB,CAAC;AAClF,OAAO,EAAE,iBAAiB,EAAgB,MAAM,yBAAyB,CAAC;AAI1E;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,2DAA2D;IAC3D,OAAO,CAAC,KAAK,CAAC,kDAAkD,CAAC,CAAC;AACpE,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,OAAO,MAAM,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,OAAO,MAAM,iBAAiB,CAAC,gBAAgB,EAAE,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IACzC,OAAO,MAAM,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW;IACzB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAID;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,OAAe,EACf,OAAiB,EAAE,EACnB,UAAqD,EAAE;IAEvD,sCAAsC;IACtC,MAAM,MAAM,GAAG,MAAM,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAE5D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,OAAO,EAAE,GAAG,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;IAC5C,CAAC;IAED,0BAA0B;IAC1B,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,mBAAmB,EAAE,CAAC;IAEjE,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;QACnC,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,wDAAwD;YAC/D,YAAY,EAAE,IAAI;SACnB,CAAC;IACJ,CAAC;IAED,oCAAoC;IACpC,MAAM,kBAAkB,GAAG,MAAM,iBAAiB,CAAC,qBAAqB,EAAE,CAAC;IAC3E,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IAErE,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE,CAAC;QACxC,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YAC/D,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,OAAO,EAAE,GAAG,cAAc,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;YACnD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS;QACX,CAAC;IACH,CAAC;IAED,OAAO;QACL,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,oCAAoC;QAC3C,YAAY,EAAE,IAAI;KACnB,CAAC;AACJ,CAAC;AAID;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,QAAQ,YAAY,CAAC,QAAQ,EAAE,CAAC;QAC9B,KAAK,OAAO;YACV,OAAO,OAAO,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC;QAClB,KAAK,OAAO;YACV,OAAO,OAAO,CAAC;QACjB;YACE,OAAO,OAAO,CAAC;IACnB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS;IACvB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC;AACjE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,QAAQ,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO;IACrB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,QAAQ,KAAK,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAClE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,KAAK;IACnB,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IACvC,OAAO,YAAY,CAAC,KAAK,CAAC;AAC5B,CAAC;AAcD,MAAM,UAAU,kBAAkB;IAChC,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;IAEvC,OAAO;QACL,IAAI,EAAE,eAAe,EAAE;QACvB,gBAAgB,EAAE,YAAY,CAAC,gBAAgB;QAC/C,UAAU,EAAE,YAAY,CAAC,UAAU;QACnC,UAAU,EAAE,YAAY,CAAC,UAAU;QACnC,cAAc,EAAE,YAAY,CAAC,KAAK;QAClC,aAAa,EAAE,YAAY,CAAC,kBAAkB,CAAC,MAAM;KACtD,CAAC;AACJ,CAAC;AAED,gDAAgD;AAChD,kBAAkB,EAAE,CAAC"}