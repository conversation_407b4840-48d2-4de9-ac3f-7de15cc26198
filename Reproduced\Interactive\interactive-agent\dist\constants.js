/**
 * Essential constants for interactive-agent
 * Minimal version based on interactive-mcp constants
 */
// User input timeout in seconds
export const USER_INPUT_TIMEOUT_SECONDS = 60;
// Platform detection and spawning constants
export const TERMINAL_SPAWN_TIMEOUT_MS = 5000;
export const MAX_SPAWN_RETRIES = 3;
export const SPAWN_RETRY_DELAY_MS = 1000;
export const PLATFORM_CACHE_TTL_MS = 300000; // 5 minutes
// Enhanced timeout constants
export const SPAWN_START_TIMEOUT_MS = 5000;
export const HEARTBEAT_TIMEOUT_MS = 10000;
export const HEARTBEAT_INTERVAL_MS = 2000;
export const PROCESS_KILL_TIMEOUT_MS = 3000;
// Additional timeout constants for consistency
export const TERMINAL_TEST_TIMEOUT_MS = 3000;
export const WINDOWS_TERMINAL_TEST_TIMEOUT_MS = 2000;
export const STALE_PROCESS_CHECK_INTERVAL_MS = 30000; // 30 seconds
export const STALE_PROCESS_TIMEOUT_MS = 30000; // 30 seconds
export const INPUT_RESPONSE_CHECK_INTERVAL_MS = 500;
// Retry configuration constants
export const DEFAULT_MAX_RETRIES = 3;
export const DEFAULT_RETRY_BASE_DELAY_MS = 1000;
export const DEFAULT_RETRY_MAX_DELAY_MS = 10000;
export const DEFAULT_BACKOFF_FACTOR = 2.0;
// File management constants
export const TEMP_FILE_PREFIX = 'interactive-agent';
// Session ID constants - using crypto.randomBytes(8).toString('hex') format
export const SESSION_ID_BYTE_LENGTH = 8; // 8 bytes = 16 hex characters
export const SESSION_ID_HEX_LENGTH = 16; // 16 hex characters
// Process management constants
export const MAX_TRACKED_PROCESSES = 100;
// Shell paths for fallback detection
export const DEFAULT_SHELL_PATHS = {
    windows: ['cmd.exe', 'powershell.exe'],
    unix: ['/bin/bash', '/bin/sh', '/bin/zsh', '/bin/fish']
};
// Terminal preferences by platform
export const TERMINAL_PREFERENCES = {
    win32: ['wt.exe', 'ConEmu64.exe', 'ConEmu.exe', 'cmd.exe'],
    darwin: ['Terminal.app', 'iTerm.app'],
    linux: ['gnome-terminal', 'konsole', 'xfce4-terminal', 'mate-terminal', 'xterm', 'urxvt']
};
//# sourceMappingURL=constants.js.map