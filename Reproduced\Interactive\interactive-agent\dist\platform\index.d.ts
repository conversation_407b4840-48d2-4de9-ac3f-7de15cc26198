export { PlatformInfo, TerminalCapabilities, getPlatformInfo, getPlatformInfoAsync, refreshPlatformCache, refreshPlatformCacheAsync, getTerminalCapabilities, getTerminalCapabilitiesAsync } from './platform-detector.js';
export { SpawnOptions, SpawnResult, spawnInTerminal, spawnWithRetry, testTerminalSpawn } from './process-spawn.js';
export { TerminalInfo, EnvironmentInfo, TerminalFallbacks } from './terminal-fallbacks.js';
export { RetryPolicy } from '../utils/retry-manager.js';
import { SpawnResult } from './process-spawn.js';
import { TerminalInfo } from './terminal-fallbacks.js';
import { PlatformType } from '../constants.js';
/**
 * Initialize platform module with orphan manager and signal handlers
 */
export declare function initializePlatform(): void;
/**
 * Check if terminal spawning is possible in current environment
 */
export declare function canSpawnTerminal(): Promise<boolean>;
/**
 * Find the best terminal for current environment
 */
export declare function findBestTerminal(): Promise<TerminalInfo | null>;
/**
 * Get available terminals for current platform
 */
export declare function getAvailableTerminals(): Promise<TerminalInfo[]>;
/**
 * Detect preferred shell for current platform
 */
export declare function detectShell(): string;
/**
 * Enhanced spawn with automatic fallback and retry logic
 */
export declare function spawnWithFallback(command: string, args?: string[], options?: import('./process-spawn.js').SpawnOptions): Promise<SpawnResult & {
    usedFallback: boolean;
}>;
/**
 * Obtient le type de plateforme simplifié
 */
export declare function getPlatformType(): PlatformType;
/**
 * Vérifie si la plateforme est Windows (incluant WSL)
 */
export declare function isWindows(): boolean;
/**
 * Vérifie si la plateforme est macOS
 */
export declare function isMacOS(): boolean;
/**
 * Vérifie si la plateforme est Linux (excluant WSL)
 */
export declare function isLinux(): boolean;
/**
 * Vérifie si nous sommes dans WSL
 */
export declare function isWSL(): boolean;
/**
 * Obtient un résumé des capacités de la plateforme
 */
export interface PlatformSummary {
    type: PlatformType;
    canSpawnTerminal: boolean;
    hasDisplay: boolean;
    isHeadless: boolean;
    preferredShell: string;
    terminalCount: number;
}
export declare function getPlatformSummary(): PlatformSummary;
//# sourceMappingURL=index.d.ts.map