import { jest } from '@jest/globals';
import { getPlatformInfo, clearPlatformCache } from './platform-detector.js';
import type { PlatformInfo } from './types.js';

// Mock Node.js modules
const mockFs = {
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
  accessSync: jest.fn(),
};

const mockOs = {
  platform: jest.fn(),
  release: jest.fn(),
  type: jest.fn(),
};

const mockProcess = {
  env: {} as Record<string, string | undefined>,
  platform: 'linux' as NodeJS.Platform,
};

// Mock modules before importing
jest.unstable_mockModule('fs', () => mockFs);
jest.unstable_mockModule('os', () => mockOs);

// Mock global process
Object.defineProperty(global, 'process', {
  value: mockProcess,
  writable: true,
});

describe('Platform Detector', () => {
  beforeEach(() => {
    // Clear cache before each test
    clearPlatformCache();
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Reset process.env
    mockProcess.env = {};
    
    // Default mock implementations
    mockOs.platform.mockReturnValue('linux');
    mockOs.release.mockReturnValue('5.4.0');
    mockOs.type.mockReturnValue('Linux');
    mockFs.existsSync.mockReturnValue(false);
    mockFs.readFileSync.mockReturnValue('');
    mockFs.accessSync.mockImplementation(() => {});
  });

  describe('Basic Platform Detection', () => {
    it('should detect Linux platform correctly', async () => {
      mockProcess.platform = 'linux';
      mockOs.platform.mockReturnValue('linux');
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.platform).toBe('linux');
      expect(platformInfo.isWSL).toBe(false);
      expect(platformInfo.isMSYS).toBe(false);
      expect(typeof platformInfo.shell).toBe('string');
      expect(Array.isArray(platformInfo.terminals)).toBe(true);
    });

    it('should detect Windows platform correctly', async () => {
      mockProcess.platform = 'win32';
      mockOs.platform.mockReturnValue('win32');
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.platform).toBe('win32');
      expect(platformInfo.isWSL).toBe(false);
      expect(typeof platformInfo.shell).toBe('string');
      expect(Array.isArray(platformInfo.terminals)).toBe(true);
    });

    it('should detect macOS platform correctly', async () => {
      mockProcess.platform = 'darwin';
      mockOs.platform.mockReturnValue('darwin');
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.platform).toBe('darwin');
      expect(platformInfo.isWSL).toBe(false);
      expect(platformInfo.isMSYS).toBe(false);
      expect(typeof platformInfo.shell).toBe('string');
      expect(Array.isArray(platformInfo.terminals)).toBe(true);
    });
  });

  describe('WSL Detection', () => {
    it('should detect WSL through /proc/version', async () => {
      mockProcess.platform = 'linux';
      mockFs.existsSync.mockImplementation((path: string) => {
        return path === '/proc/version';
      });
      mockFs.readFileSync.mockImplementation((path: string) => {
        if (path === '/proc/version') {
          return 'Linux version 5.4.0-Microsoft-standard-WSL2';
        }
        return '';
      });
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isWSL).toBe(true);
      expect(platformInfo.platform).toBe('linux');
    });

    it('should detect WSL through environment variables', async () => {
      mockProcess.platform = 'linux';
      mockProcess.env.WSL_DISTRO_NAME = 'Ubuntu';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isWSL).toBe(true);
    });

    it('should detect WSL through /proc/sys/kernel/osrelease', async () => {
      mockProcess.platform = 'linux';
      mockFs.existsSync.mockImplementation((path: string) => {
        return path === '/proc/sys/kernel/osrelease';
      });
      mockFs.readFileSync.mockImplementation((path: string) => {
        if (path === '/proc/sys/kernel/osrelease') {
          return '5.4.0-Microsoft-standard-WSL2';
        }
        return '';
      });
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isWSL).toBe(true);
    });
  });

  describe('MSYS Detection', () => {
    it('should detect MSYS through MSYSTEM environment variable', async () => {
      mockProcess.platform = 'win32';
      mockProcess.env.MSYSTEM = 'MINGW64';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isMSYS).toBe(true);
      expect(platformInfo.platform).toBe('win32');
    });

    it('should detect MSYS through MSYS environment variable', async () => {
      mockProcess.platform = 'win32';
      mockProcess.env.MSYS = '1';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isMSYS).toBe(true);
    });

    it('should detect MSYS through MINGW_PREFIX', async () => {
      mockProcess.platform = 'win32';
      mockProcess.env.MINGW_PREFIX = '/mingw64';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isMSYS).toBe(true);
    });
  });

  describe('Shell Detection', () => {
    it('should detect shell from SHELL environment variable', async () => {
      mockProcess.env.SHELL = '/bin/bash';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.shell).toBe('/bin/bash');
    });

    it('should fallback to platform default shell', async () => {
      mockProcess.platform = 'win32';
      delete mockProcess.env.SHELL;
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.shell).toMatch(/cmd\.exe|powershell\.exe|pwsh\.exe/);
    });

    it('should detect PowerShell on Windows', async () => {
      mockProcess.platform = 'win32';
      mockProcess.env.PSModulePath = 'C:\\Program Files\\PowerShell\\Modules';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.shell).toMatch(/powershell|pwsh/);
    });
  });

  describe('Display Detection', () => {
    it('should detect X11 display', async () => {
      mockProcess.env.DISPLAY = ':0';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.hasDisplay).toBe(true);
      expect(platformInfo.isHeadless).toBe(false);
    });

    it('should detect Wayland display', async () => {
      mockProcess.env.WAYLAND_DISPLAY = 'wayland-0';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.hasDisplay).toBe(true);
      expect(platformInfo.isHeadless).toBe(false);
    });

    it('should detect headless environment', async () => {
      delete mockProcess.env.DISPLAY;
      delete mockProcess.env.WAYLAND_DISPLAY;
      mockProcess.env.CI = 'true';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.hasDisplay).toBe(false);
      expect(platformInfo.isHeadless).toBe(true);
    });

    it('should detect SSH session as headless', async () => {
      mockProcess.env.SSH_CLIENT = '192.168.1.1 12345 22';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.isHeadless).toBe(true);
    });
  });

  describe('Terminal Detection', () => {
    it('should detect available terminals on Linux', async () => {
      mockProcess.platform = 'linux';
      mockFs.accessSync.mockImplementation((path: string) => {
        if (path.includes('gnome-terminal') || path.includes('xterm')) {
          return; // No error means accessible
        }
        throw new Error('Not accessible');
      });
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.terminals).toContain('gnome-terminal');
      expect(platformInfo.terminals.length).toBeGreaterThan(0);
    });

    it('should detect Windows terminals', async () => {
      mockProcess.platform = 'win32';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.terminals).toContain('cmd.exe');
      expect(platformInfo.terminals.length).toBeGreaterThan(0);
    });

    it('should detect macOS terminals', async () => {
      mockProcess.platform = 'darwin';
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo.terminals).toContain('Terminal.app');
      expect(platformInfo.terminals.length).toBeGreaterThan(0);
    });
  });

  describe('Caching Behavior', () => {
    it('should cache platform info after first call', async () => {
      const firstCall = await getPlatformInfo();
      const secondCall = await getPlatformInfo();
      
      expect(firstCall).toBe(secondCall); // Same object reference
      expect(mockOs.platform).toHaveBeenCalledTimes(1);
    });

    it('should allow cache clearing', async () => {
      await getPlatformInfo();
      clearPlatformCache();
      await getPlatformInfo();
      
      expect(mockOs.platform).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle file system errors gracefully', async () => {
      mockFs.existsSync.mockImplementation(() => {
        throw new Error('File system error');
      });
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo).toBeDefined();
      expect(platformInfo.platform).toBeDefined();
    });

    it('should handle missing environment variables', async () => {
      mockProcess.env = {};
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo).toBeDefined();
      expect(typeof platformInfo.shell).toBe('string');
    });

    it('should provide fallback values for all properties', async () => {
      // Simulate complete failure of detection methods
      mockOs.platform.mockImplementation(() => {
        throw new Error('OS detection failed');
      });
      
      const platformInfo = await getPlatformInfo();
      
      expect(platformInfo).toBeDefined();
      expect(typeof platformInfo.platform).toBe('string');
      expect(typeof platformInfo.isWSL).toBe('boolean');
      expect(typeof platformInfo.isMSYS).toBe('boolean');
      expect(typeof platformInfo.shell).toBe('string');
      expect(typeof platformInfo.hasDisplay).toBe('boolean');
      expect(typeof platformInfo.isHeadless).toBe('boolean');
      expect(Array.isArray(platformInfo.terminals)).toBe(true);
    });
  });

  describe('Performance', () => {
    it('should complete platform detection quickly', async () => {
      const startTime = Date.now();
      await getPlatformInfo();
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should use cache for subsequent calls', async () => {
      const startTime = Date.now();
      await getPlatformInfo();
      await getPlatformInfo();
      await getPlatformInfo();
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Cached calls should be very fast
    });
  });
});
