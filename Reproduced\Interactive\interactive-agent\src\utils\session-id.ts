import * as crypto from 'crypto';
import { SESSION_ID_BYTE_LENGTH } from '../constants.js';

/**
 * Generate a cryptographically secure session ID
 * Uses crypto.randomBytes for security and consistency across the application
 *
 * @param byteLength - Optional length in bytes (default from constants)
 * @returns A hexadecimal session ID string (16 hex chars = 8 bytes)
 */
export function generateSessionId(byteLength?: number): string {
  const length = byteLength || SESSION_ID_BYTE_LENGTH;
  return crypto.randomBytes(length).toString('hex');
}

/**
 * Generate a session ID with a specific prefix
 * Useful for creating identifiable session IDs for different purposes
 * 
 * @param prefix - Prefix to add to the session ID
 * @param length - Optional length in bytes for the random part
 * @returns A prefixed session ID string
 */
export function generatePrefixedSessionId(prefix: string, length?: number): string {
  const sessionId = generateSessionId(length);
  return `${prefix}-${sessionId}`;
}

/**
 * Validate if a string looks like a valid session ID
 * Checks if it's a hexadecimal string of expected length
 * 
 * @param sessionId - The session ID to validate
 * @returns True if the session ID appears valid
 */
export function isValidSessionId(sessionId: string): boolean {
  if (!sessionId || typeof sessionId !== 'string') {
    return false;
  }
  
  // Remove any prefix (everything before the last dash)
  const lastDashIndex = sessionId.lastIndexOf('-');
  const idPart = lastDashIndex >= 0 ? sessionId.substring(lastDashIndex + 1) : sessionId;
  
  // Check if it's a valid hex string of reasonable length
  const hexPattern = /^[0-9a-f]+$/i;
  return hexPattern.test(idPart) && idPart.length >= 8 && idPart.length <= 32;
}

/**
 * Extract the session ID part from a prefixed session ID
 * 
 * @param prefixedSessionId - A session ID that may have a prefix
 * @returns The session ID part without prefix
 */
export function extractSessionId(prefixedSessionId: string): string {
  const lastDashIndex = prefixedSessionId.lastIndexOf('-');
  return lastDashIndex >= 0 ? prefixedSessionId.substring(lastDashIndex + 1) : prefixedSessionId;
}
