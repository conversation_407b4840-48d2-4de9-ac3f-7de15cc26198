/**
 * Interface for temporary file objects with automatic cleanup
 */
export interface TempFile {
    readonly path: string;
    readonly isDisposed: boolean;
    dispose(): Promise<void>;
}
/**
 * Comprehensive temporary file management system with automatic cleanup
 */
export declare class TempFileManager {
    private static instance;
    private readonly resources;
    private readonly finalizationRegistry;
    private cleanupHandlersRegistered;
    private constructor();
    /**
     * Get singleton instance of TempFileManager
     */
    static getInstance(): TempFileManager;
    /**
     * Create a temporary file with unique session ID
     */
    createTempFile(prefix: string, extension?: string, content?: string): Promise<TempFile>;
    /**
     * Create a temporary directory
     */
    createTempDir(prefix?: string): Promise<TempFile>;
    /**
     * Register an existing file or directory for cleanup
     */
    registerForCleanup(filePath: string, sessionId?: string): void;
    /**
     * Dispose of all managed resources
     */
    disposeAll(): Promise<void>;
    /**
     * Internal method to dispose of a specific resource
     */
    disposeResource(filePath: string): Promise<void>;
    /**
     * Generate a unique session ID using centralized utility
     */
    private generateSessionId;
    /**
     * Generate a filename with session ID
     */
    private generateFileName;
    /**
     * Check if path exists and is a directory
     */
    private isDirectory;
    /**
     * Cleanup a specific resource
     */
    private cleanupResource;
    /**
     * Register process exit handlers for cleanup
     */
    private registerCleanupHandlers;
}
export declare const tempFileManager: TempFileManager;
//# sourceMappingURL=temp-file-manager.d.ts.map