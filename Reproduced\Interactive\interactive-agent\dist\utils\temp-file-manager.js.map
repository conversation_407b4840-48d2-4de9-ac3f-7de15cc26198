{"version": 3, "file": "temp-file-manager.js", "sourceRoot": "", "sources": ["../../src/utils/temp-file-manager.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,EAAE,MAAM,IAAI,CAAC;AACzB,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AACpD,OAAO,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAqB1D;;GAEG;AACH,MAAM,OAAO,eAAe;IAClB,MAAM,CAAC,QAAQ,GAA2B,IAAI,CAAC;IACtC,SAAS,GAAG,IAAI,GAAG,EAAwB,CAAC;IAC5C,oBAAoB,CAA+B;IAC5D,yBAAyB,GAAG,KAAK,CAAC;IAE1C;QACE,6DAA6D;QAC7D,IAAI,CAAC,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,CAAC,IAAY,EAAE,EAAE;YACpE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACrC,OAAO,CAAC,IAAI,CAAC,yCAAyC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;YACrE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,cAAc,CACzB,MAAc,EACd,SAAkB,EAClB,OAAgB;QAEhB,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACrE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,kBAAkB;YAClB,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;gBAC1B,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;YACpD,CAAC;YAED,wBAAwB;YACxB,MAAM,QAAQ,GAAiB;gBAC7B,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,KAAK;gBAClB,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEvC,yBAAyB;YACzB,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAElD,0BAA0B;YAC1B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAEvD,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CAAC,MAAe;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,MAAM,EAAE,SAAS,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAEtD,wBAAwB;YACxB,MAAM,QAAQ,GAAiB;gBAC7B,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,IAAI;gBACjB,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEtC,yBAAyB;YACzB,MAAM,OAAO,GAAG,IAAI,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAEhD,0BAA0B;YAC1B,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAErD,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,oCAAoC,KAAK,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,QAAgB,EAAE,SAAkB;QAC5D,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,qBAAqB;QAC/B,CAAC;QAED,MAAM,QAAQ,GAAiB;YAC7B,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;YACvC,SAAS,EAAE,SAAS,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAChD,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU;QACrB,MAAM,eAAe,GAAoB,EAAE,CAAC;QAE5C,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACxC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC3C,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,OAAO,iBAAiB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAc,EAAE,SAAiB,EAAE,SAAkB;QAC5E,MAAM,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvF,OAAO,GAAG,MAAM,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,QAAgB;QAClC,IAAI,CAAC;YACH,OAAO,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;QAC7C,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,QAAgB;QAC5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,CAAC,cAAc;YACxB,CAAC;YAED,iCAAiC;YACjC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBACzB,MAAM,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YACzD,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACrC,CAAC;YAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,mDAAmD;YACnD,OAAO,CAAC,IAAI,CAAC,+BAA+B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACnC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,GAAG,EAAE;YACnB,8BAA8B;YAC9B,KAAK,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBAClD,IAAI,CAAC;oBACH,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;4BACzB,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;wBACxD,CAAC;6BAAM,CAAC;4BACN,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBAC1B,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,IAAI,CAAC,8BAA8B,QAAQ,EAAE,EAAE,KAAK,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,SAAS;gBAAE,OAAO;YACtB,SAAS,GAAG,IAAI,CAAC;YACjB,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAEvC,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,EAAE;YAC1C,IAAI,SAAS;gBAAE,OAAO;YAEtB,OAAO,CAAC,KAAK,CAAC,6BAA6B,MAAM,gCAAgC,CAAC,CAAC;YAEnF,mCAAmC;YACnC,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;gBACtC,OAAO,CAAC,IAAI,CAAC,4DAA4D,CAAC,CAAC;gBAC3E,kBAAkB,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,EAAE,uBAAuB,CAAC,CAAC;YAE5B,IAAI,CAAC;gBACH,OAAO,EAAE,CAAC;gBACV,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,kDAAkD,EAAE,KAAK,CAAC,CAAC;gBACxE,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9B,kBAAkB,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;QAEzD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;IACxC,CAAC;;AAGH;;GAEG;AACH,MAAM,YAAY;IAIE;IACC;IAJX,WAAW,GAAG,KAAK,CAAC;IAE5B,YACkB,IAAY,EACX,OAAwB;QADzB,SAAI,GAAJ,IAAI,CAAQ;QACX,YAAO,GAAP,OAAO,CAAiB;IACxC,CAAC;IAEJ,IAAW,UAAU;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,OAAO;QAClB,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,CAAC,mBAAmB;QAC7B,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QACxB,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,CAAC,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC"}