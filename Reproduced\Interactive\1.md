Create a `jest.setup.js` file in the project root that provides global test setup including mocks for Node.js built-ins (fs, os, child_process) and any custom test utilities needed for the platform detection and process spawning tests.

Verify and fix the import paths in all test files to match the actual compiled module structure. Consider using the `@/` path alias consistently or adjust imports to match the built output in the `dist` directory. Also consider replacing `jest.unstable_mockModule()` with more stable mocking approaches.

Enhance the `build-all.js` script to include upfront validation of required tools (tsc, tsc-alias), implement rollback mechanisms for failed builds, and provide better error recovery with actionable suggestions for common failure scenarios.

Reorganize the npm scripts in `package.json` with more consistent naming, add missing development workflow scripts like `dev:build`, create `validate:quick` and `validate:full` variants, and add a `postinstall` script for development setup.