// Jest setup file for interactive-agent tests

// Mock Node.js built-in modules for consistent testing
import { jest } from '@jest/globals';

// Global test utilities
global.testUtils = {
  // Mock platform detection
  mockPlatform: (platform) => {
    Object.defineProperty(process, 'platform', {
      value: platform,
      writable: true,
    });
  },
  
  // Mock environment variables
  mockEnv: (env) => {
    const originalEnv = process.env;
    process.env = { ...originalEnv, ...env };
    return () => {
      process.env = originalEnv;
    };
  },
  
  // Create mock child process
  createMockChildProcess: () => {
    const EventEmitter = require('events');
    const mockProcess = new EventEmitter();
    mockProcess.pid = Math.floor(Math.random() * 10000);
    mockProcess.stdout = new EventEmitter();
    mockProcess.stderr = new EventEmitter();
    mockProcess.stdin = {
      write: jest.fn(),
      end: jest.fn(),
    };
    mockProcess.kill = jest.fn();
    mockProcess.disconnect = jest.fn();
    return mockProcess;
  },
  
  // Simulate async delays for testing
  delay: (ms) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate random test data
  randomString: (length = 10) => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // Create temporary test directory path
  getTempTestPath: () => {
    const os = require('os');
    const path = require('path');
    return path.join(os.tmpdir(), 'interactive-agent-test', global.testUtils.randomString());
  },
};

// Global mocks for common Node.js modules
const mockFs = {
  existsSync: jest.fn(),
  readFileSync: jest.fn(),
  writeFileSync: jest.fn(),
  mkdirSync: jest.fn(),
  unlinkSync: jest.fn(),
  rmSync: jest.fn(),
  mkdtempSync: jest.fn(),
  accessSync: jest.fn(),
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    unlink: jest.fn(),
    rm: jest.fn(),
    mkdtemp: jest.fn(),
    access: jest.fn(),
  },
};

const mockOs = {
  platform: jest.fn(),
  release: jest.fn(),
  type: jest.fn(),
  tmpdir: jest.fn(),
  homedir: jest.fn(),
  userInfo: jest.fn(),
};

const mockPath = {
  join: jest.fn(),
  resolve: jest.fn(),
  dirname: jest.fn(),
  basename: jest.fn(),
  extname: jest.fn(),
  sep: process.platform === 'win32' ? '\\' : '/',
  delimiter: process.platform === 'win32' ? ';' : ':',
};

const mockChildProcess = {
  spawn: jest.fn(),
  exec: jest.fn(),
  execSync: jest.fn(),
  fork: jest.fn(),
};

// Store original modules for restoration
global.originalModules = {
  fs: require('fs'),
  os: require('os'),
  path: require('path'),
  child_process: require('child_process'),
};

// Make mocks available globally
global.mockFs = mockFs;
global.mockOs = mockOs;
global.mockPath = mockPath;
global.mockChildProcess = mockChildProcess;

// Setup default mock implementations
beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Default fs mock implementations
  mockFs.existsSync.mockReturnValue(false);
  mockFs.readFileSync.mockReturnValue('');
  mockFs.writeFileSync.mockImplementation(() => {});
  mockFs.mkdirSync.mockImplementation(() => {});
  mockFs.unlinkSync.mockImplementation(() => {});
  mockFs.rmSync.mockImplementation(() => {});
  mockFs.mkdtempSync.mockReturnValue('/tmp/test-dir-123');
  mockFs.accessSync.mockImplementation(() => {});
  
  mockFs.promises.readFile.mockResolvedValue('');
  mockFs.promises.writeFile.mockResolvedValue(undefined);
  mockFs.promises.mkdir.mockResolvedValue(undefined);
  mockFs.promises.unlink.mockResolvedValue(undefined);
  mockFs.promises.rm.mockResolvedValue(undefined);
  mockFs.promises.mkdtemp.mockResolvedValue('/tmp/test-dir-123');
  mockFs.promises.access.mockResolvedValue(undefined);
  
  // Default os mock implementations
  mockOs.platform.mockReturnValue(process.platform);
  mockOs.release.mockReturnValue('1.0.0');
  mockOs.type.mockReturnValue('Linux');
  mockOs.tmpdir.mockReturnValue('/tmp');
  mockOs.homedir.mockReturnValue('/home/<USER>');
  mockOs.userInfo.mockReturnValue({ username: 'testuser', uid: 1000, gid: 1000 });
  
  // Default path mock implementations
  mockPath.join.mockImplementation((...args) => args.join(mockPath.sep));
  mockPath.resolve.mockImplementation((...args) => mockPath.sep + args.join(mockPath.sep));
  mockPath.dirname.mockImplementation((path) => {
    const parts = path.split(mockPath.sep);
    return parts.slice(0, -1).join(mockPath.sep) || mockPath.sep;
  });
  mockPath.basename.mockImplementation((path) => {
    return path.split(mockPath.sep).pop() || '';
  });
  mockPath.extname.mockImplementation((path) => {
    const basename = path.split(mockPath.sep).pop() || '';
    const lastDot = basename.lastIndexOf('.');
    return lastDot > 0 ? basename.slice(lastDot) : '';
  });
  
  // Default child_process mock implementations
  mockChildProcess.spawn.mockReturnValue(global.testUtils.createMockChildProcess());
  mockChildProcess.exec.mockImplementation((command, callback) => {
    if (callback) callback(null, 'mock output', '');
  });
  mockChildProcess.execSync.mockReturnValue('mock output');
  mockChildProcess.fork.mockReturnValue(global.testUtils.createMockChildProcess());
});

// Cleanup after each test
afterEach(() => {
  // Restore original process properties if they were modified
  if (global.originalProcess) {
    Object.assign(process, global.originalProcess);
    delete global.originalProcess;
  }
  
  // Clear any timers that might be hanging around
  jest.clearAllTimers();
});

// Global error handling for tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection in test:', reason);
  // Don't exit in tests, just log the error
});

// Increase timeout for platform-specific tests that might be slower
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests unless explicitly testing them
const originalConsole = { ...console };
global.mockConsole = () => {
  console.log = jest.fn();
  console.error = jest.fn();
  console.warn = jest.fn();
  console.info = jest.fn();
  console.debug = jest.fn();
};

global.restoreConsole = () => {
  Object.assign(console, originalConsole);
};

// Platform-specific test helpers
global.platformTests = {
  // Skip test on specific platforms
  skipOn: (platforms, reason = 'Platform not supported') => {
    const currentPlatform = process.platform;
    const shouldSkip = Array.isArray(platforms) 
      ? platforms.includes(currentPlatform)
      : platforms === currentPlatform;
    
    if (shouldSkip) {
      return test.skip;
    }
    return test;
  },
  
  // Only run test on specific platforms
  onlyOn: (platforms, reason = 'Platform specific test') => {
    const currentPlatform = process.platform;
    const shouldRun = Array.isArray(platforms)
      ? platforms.includes(currentPlatform)
      : platforms === currentPlatform;
    
    if (!shouldRun) {
      return test.skip;
    }
    return test;
  },
  
  // Run test with different platform contexts
  withPlatform: (platform, testFn) => {
    return () => {
      const originalPlatform = process.platform;
      global.testUtils.mockPlatform(platform);
      
      try {
        return testFn();
      } finally {
        global.testUtils.mockPlatform(originalPlatform);
      }
    };
  },
};

// Export test utilities for use in test files
export { global as testGlobals };
