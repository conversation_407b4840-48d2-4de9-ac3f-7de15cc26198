/**
 * Terminal information with capabilities
 */
export interface TerminalInfo {
    name: string;
    command: string;
    available: boolean;
    tested: boolean;
    priority: number;
}
/**
 * Environment detection results
 */
export interface EnvironmentInfo {
    isSSH: boolean;
    isDocker: boolean;
    isCI: boolean;
    isHeadless: boolean;
    hasDisplay: boolean;
}
/**
 * Terminal detection and fallback system
 */
export declare class TerminalFallbacks {
    private static terminalCache;
    private static environmentInfo;
    /**
     * Terminal preferences by platform with priority ordering
     */
    private static readonly TERMINAL_PREFERENCES;
    /**
     * Detect current environment characteristics
     */
    static detectEnvironment(): EnvironmentInfo;
    /**
     * Test if a command/terminal is available in the system
     * Alias for testTerminalAvailability for backward compatibility
     */
    static isCommandAvailable(command: string): Promise<boolean>;
    /**
     * Test if a macOS application exists in /Applications/
     */
    private static testMacOSApplication;
    /**
     * Test if Windows Terminal is available via registry or PATH
     */
    private static testWindowsTerminal;
    /**
     * Validate environment variable overrides
     */
    private static validateEnvironmentOverride;
    /**
     * Test if a terminal is available and working with platform-specific checks
     */
    static testTerminalAvailability(terminalName: string): Promise<boolean>;
    /**
     * Generic terminal test using spawn
     */
    private static genericTerminalTest;
    /**
     * Find the best working terminal from a list
     */
    static findWorkingTerminal(terminalList: TerminalInfo[]): Promise<TerminalInfo | null>;
    /**
     * Get available terminals for current platform
     */
    static getAvailableTerminals(): Promise<TerminalInfo[]>;
    /**
     * Find the best terminal for current environment
     */
    static findBestTerminal(): Promise<TerminalInfo | null>;
    /**
     * Check if terminal spawning is possible in current environment
     */
    static canSpawnTerminal(): Promise<boolean>;
    /**
     * Get fallback strategy for current environment
     */
    static getFallbackStrategy(): 'terminal' | 'console' | 'none';
    /**
     * Clear terminal availability cache
     */
    static clearCache(): void;
    /**
     * Get terminal preferences with validated environment overrides
     */
    static getTerminalPreferences(): string[];
}
//# sourceMappingURL=terminal-fallbacks.d.ts.map