/**
 * Error types that should trigger retries
 */
export const RETRY<PERSON>LE_ERROR_CODES = {
    // Spawn errors
    ENOENT: 'ENOENT', // Command not found
    EACCES: 'EACCES', // Permission denied
    EMFILE: 'EMFILE', // Too many open files
    ENFILE: 'ENFILE', // File table overflow
    EAGAIN: 'EAGAIN', // Resource temporarily unavailable
    // Process errors
    SPAWN_FAILED: 'SPAWN_FAILED',
    SPAWN_TIMEOUT: 'SPAWN_TIMEOUT',
    // Heartbeat errors
    HEARTBEAT_TIMEOUT: 'HEARTBEAT_TIMEOUT',
    // File operation errors
    EBUSY: 'EBUSY', // Resource busy
    ENOTEMPTY: 'ENOTEMPTY', // Directory not empty
};
/**
 * Pre-configured retry policies for common scenarios
 */
export const RETRY_POLICIES = {
    SPAWN_RETRY_POLICY: {
        maxRetries: 3,
        baseDelayMs: 1000,
        maxDelayMs: 10000,
        backoffFactor: 2.0,
        retryableErrors: [
            RETRYABLE_ERROR_CODES.ENOENT,
            RETRYABLE_ERROR_CODES.EACCES,
            R<PERSON><PERSON><PERSON><PERSON>LE_ERROR_CODES.EMFILE,
            RETRYABLE_ERROR_CODES.ENFILE,
            RETRYABLE_ERROR_CODES.EAGAIN,
            RETRYABLE_ERROR_CODES.SPAWN_FAILED,
            RETRYABLE_ERROR_CODES.SPAWN_TIMEOUT,
        ]
    },
    FILE_OPERATION_RETRY_POLICY: {
        maxRetries: 2,
        baseDelayMs: 500,
        maxDelayMs: 5000,
        backoffFactor: 1.5,
        retryableErrors: [
            RETRYABLE_ERROR_CODES.EBUSY,
            RETRYABLE_ERROR_CODES.ENOTEMPTY,
            RETRYABLE_ERROR_CODES.EAGAIN,
        ]
    },
    NETWORK_RETRY_POLICY: {
        maxRetries: 5,
        baseDelayMs: 2000,
        maxDelayMs: 30000,
        backoffFactor: 2.0,
        retryableErrors: [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
        ]
    }
};
/**
 * Intelligent retry system for handling failures with exponential backoff
 */
export class RetryManager {
    /**
     * Calculate delay for a given attempt with jitter
     */
    static calculateDelay(attempt, policy) {
        const exponentialDelay = policy.baseDelayMs * Math.pow(policy.backoffFactor, attempt - 1);
        const cappedDelay = Math.min(exponentialDelay, policy.maxDelayMs);
        // Add jitter (±25%) to avoid thundering herd
        const jitter = cappedDelay * 0.25 * (Math.random() - 0.5);
        return Math.max(0, cappedDelay + jitter);
    }
    /**
     * Check if an error should trigger a retry
     */
    static isRetryableError(error, policy) {
        const errorCode = error.code || error.name || error.message;
        return policy.retryableErrors.some(retryableCode => errorCode.includes(retryableCode) || error.message.includes(retryableCode));
    }
    /**
     * Execute an operation with retry logic
     */
    static async executeWithRetry(operation, policy, abortSignal) {
        const errors = [];
        let totalDelay = 0;
        let lastError;
        for (let attempt = 1; attempt <= policy.maxRetries + 1; attempt++) {
            try {
                // Check if operation was aborted
                if (abortSignal?.aborted) {
                    throw new Error('Operation aborted');
                }
                const result = await operation(abortSignal);
                return {
                    success: true,
                    result,
                    attempts: attempt,
                    totalDelay,
                    errors
                };
            }
            catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));
                lastError = err;
                errors.push(err);
                const isLastAttempt = attempt > policy.maxRetries;
                // Log retry attempt
                console.warn(`Retry attempt ${attempt}/${policy.maxRetries + 1} failed:`, {
                    error: err.message,
                    code: err.code,
                    isLastAttempt,
                    willRetry: !isLastAttempt && this.isRetryableError(err, policy)
                });
                // If this is the last attempt or error is not retryable, fail
                if (isLastAttempt || !this.isRetryableError(err, policy)) {
                    break;
                }
                // Calculate and apply delay
                const delay = this.calculateDelay(attempt, policy);
                totalDelay += delay;
                // Wait before retry, but check for abort signal
                await this.delay(delay, abortSignal);
            }
        }
        // Create aggregated error with all attempt information
        const aggregatedError = new Error(`Operation failed after ${errors.length} attempts. Last error: ${lastError?.message}`);
        aggregatedError.attempts = errors.length;
        aggregatedError.errors = errors;
        aggregatedError.totalDelay = totalDelay;
        return {
            success: false,
            error: aggregatedError,
            attempts: errors.length,
            totalDelay,
            errors
        };
    }
    /**
     * Delay with abort signal support
     */
    static async delay(ms, abortSignal) {
        return new Promise((resolve, reject) => {
            if (abortSignal?.aborted) {
                reject(new Error('Operation aborted'));
                return;
            }
            const timeout = setTimeout(resolve, ms);
            const abortHandler = () => {
                clearTimeout(timeout);
                reject(new Error('Operation aborted'));
            };
            abortSignal?.addEventListener('abort', abortHandler, { once: true });
            // Clean up listener when timeout completes
            setTimeout(() => {
                abortSignal?.removeEventListener('abort', abortHandler);
            }, ms);
        });
    }
    /**
     * Create a retry policy with custom overrides
     */
    static createPolicy(basePolicy, overrides) {
        return {
            ...basePolicy,
            ...overrides,
            retryableErrors: overrides.retryableErrors || basePolicy.retryableErrors
        };
    }
    /**
     * Wrap a function to automatically apply retry logic
     */
    static withRetry(fn, policy) {
        return async (...args) => {
            const result = await this.executeWithRetry(() => fn(...args), policy);
            if (result.success) {
                return result.result;
            }
            else {
                throw result.error;
            }
        };
    }
}
//# sourceMappingURL=retry-manager.js.map