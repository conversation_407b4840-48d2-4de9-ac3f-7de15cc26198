#!/usr/bin/env node

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, rmSync, statSync } from 'fs';
import { performance } from 'perf_hooks';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname, '..');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Build configuration
const config = {
  mode: process.argv[2] || 'development', // development, production, or clean
  verbose: process.argv.includes('--verbose'),
  watch: process.argv.includes('--watch'),
  typeCheck: !process.argv.includes('--no-type-check'),
  sourceMaps: !process.argv.includes('--no-source-maps'),
  clean: process.argv.includes('--clean') || process.argv[2] === 'clean',
};

// Build metrics
const metrics = {
  startTime: 0,
  endTime: 0,
  steps: {},
  outputSize: 0,
  errors: [],
  warnings: [],
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logVerbose(message, color = 'reset') {
  if (config.verbose) {
    console.log(`${colors[color]}  ${message}${colors.reset}`);
  }
}

function logStep(step, color = 'cyan') {
  log(`\n🔧 ${step}...`, color);
  metrics.steps[step] = { startTime: performance.now() };
}

function logStepComplete(step, success = true) {
  const stepMetrics = metrics.steps[step];
  if (stepMetrics) {
    stepMetrics.endTime = performance.now();
    stepMetrics.duration = stepMetrics.endTime - stepMetrics.startTime;
    stepMetrics.success = success;
  }
  
  const duration = stepMetrics ? ` (${stepMetrics.duration.toFixed(2)}ms)` : '';
  const icon = success ? '✅' : '❌';
  log(`${icon} ${step} ${success ? 'completed' : 'failed'}${duration}`, success ? 'green' : 'red');
}

async function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    logVerbose(`Running: ${command} ${args.join(' ')}`, 'blue');
    
    const child = spawn(command, args, {
      cwd: projectRoot,
      stdio: config.verbose ? 'inherit' : 'pipe',
      shell: process.platform === 'win32',
      ...options,
    });
    
    let stdout = '';
    let stderr = '';
    
    if (!config.verbose) {
      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });
    }
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve({ stdout, stderr, code });
      } else {
        reject(new Error(`Command failed with code ${code}\nstdout: ${stdout}\nstderr: ${stderr}`));
      }
    });
    
    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function cleanBuild() {
  logStep('Cleaning build artifacts');
  
  try {
    const dirsToClean = ['dist', 'coverage', '.tsbuildinfo'];
    
    for (const dir of dirsToClean) {
      const dirPath = join(projectRoot, dir);
      if (existsSync(dirPath)) {
        rmSync(dirPath, { recursive: true, force: true });
        logVerbose(`Removed ${dir}`, 'yellow');
      }
    }
    
    logStepComplete('Cleaning build artifacts');
    return true;
  } catch (error) {
    metrics.errors.push(`Clean failed: ${error.message}`);
    logStepComplete('Cleaning build artifacts', false);
    return false;
  }
}

async function typeCheck() {
  if (!config.typeCheck) {
    logVerbose('Skipping type check', 'yellow');
    return true;
  }
  
  logStep('Type checking');
  
  try {
    await runCommand('npx', ['tsc', '--noEmit']);
    logStepComplete('Type checking');
    return true;
  } catch (error) {
    metrics.errors.push(`Type check failed: ${error.message}`);
    logStepComplete('Type checking', false);
    return false;
  }
}

async function buildTypeScript() {
  logStep('Compiling TypeScript');
  
  try {
    const tscArgs = ['tsc'];
    
    // Add build-specific flags
    if (config.mode === 'production') {
      tscArgs.push('--declaration');
      if (!config.sourceMaps) {
        tscArgs.push('--sourceMap', 'false');
      }
    } else if (config.mode === 'development') {
      if (config.sourceMaps) {
        tscArgs.push('--sourceMap');
      }
    }
    
    if (config.watch) {
      tscArgs.push('--watch');
    }
    
    await runCommand('npx', tscArgs);
    logStepComplete('Compiling TypeScript');
    return true;
  } catch (error) {
    metrics.errors.push(`TypeScript compilation failed: ${error.message}`);
    logStepComplete('Compiling TypeScript', false);
    return false;
  }
}

async function resolvePathAliases() {
  logStep('Resolving path aliases');
  
  try {
    await runCommand('npx', ['tsc-alias']);
    logStepComplete('Resolving path aliases');
    return true;
  } catch (error) {
    metrics.errors.push(`Path alias resolution failed: ${error.message}`);
    logStepComplete('Resolving path aliases', false);
    return false;
  }
}

async function validateBuild() {
  logStep('Validating build output');
  
  try {
    const distPath = join(projectRoot, 'dist');
    const indexPath = join(distPath, 'index.js');
    
    if (!existsSync(distPath)) {
      throw new Error('dist directory not found');
    }
    
    if (!existsSync(indexPath)) {
      throw new Error('index.js not found in dist');
    }
    
    // Calculate output size
    const calculateSize = (dirPath) => {
      let totalSize = 0;
      const items = require('fs').readdirSync(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = join(dirPath, item.name);
        if (item.isDirectory()) {
          totalSize += calculateSize(itemPath);
        } else {
          totalSize += statSync(itemPath).size;
        }
      }
      
      return totalSize;
    };
    
    metrics.outputSize = calculateSize(distPath);
    
    logVerbose(`Output size: ${(metrics.outputSize / 1024).toFixed(2)} KB`, 'blue');
    
    // Basic import test
    try {
      const indexModule = await import(indexPath);
      logVerbose('Build output imports successfully', 'green');
    } catch (importError) {
      metrics.warnings.push(`Build output import test failed: ${importError.message}`);
      logVerbose('Build output import test failed', 'yellow');
    }
    
    logStepComplete('Validating build output');
    return true;
  } catch (error) {
    metrics.errors.push(`Build validation failed: ${error.message}`);
    logStepComplete('Validating build output', false);
    return false;
  }
}

async function runTests() {
  if (config.mode === 'production' || process.argv.includes('--no-test')) {
    logVerbose('Skipping tests', 'yellow');
    return true;
  }
  
  logStep('Running tests');
  
  try {
    await runCommand('npm', ['test']);
    logStepComplete('Running tests');
    return true;
  } catch (error) {
    metrics.warnings.push(`Tests failed: ${error.message}`);
    logStepComplete('Running tests', false);
    return false; // Don't fail build for test failures in development
  }
}

function printBuildSummary() {
  log('\n📊 Build Summary', 'bright');
  log('═'.repeat(50), 'blue');
  
  const totalTime = metrics.endTime - metrics.startTime;
  log(`Total Build Time: ${totalTime.toFixed(2)}ms`, 'blue');
  log(`Output Size: ${(metrics.outputSize / 1024).toFixed(2)} KB`, 'blue');
  log(`Mode: ${config.mode}`, 'blue');
  
  // Step breakdown
  if (config.verbose) {
    log('\n📋 Step Breakdown:', 'cyan');
    for (const [step, stepMetrics] of Object.entries(metrics.steps)) {
      if (stepMetrics.duration) {
        const percentage = ((stepMetrics.duration / totalTime) * 100).toFixed(1);
        const status = stepMetrics.success ? '✅' : '❌';
        log(`  ${status} ${step}: ${stepMetrics.duration.toFixed(2)}ms (${percentage}%)`, 
            stepMetrics.success ? 'green' : 'red');
      }
    }
  }
  
  // Errors and warnings
  if (metrics.errors.length > 0) {
    log('\n❌ Errors:', 'red');
    for (const error of metrics.errors) {
      log(`  ${error}`, 'red');
    }
  }
  
  if (metrics.warnings.length > 0) {
    log('\n⚠️  Warnings:', 'yellow');
    for (const warning of metrics.warnings) {
      log(`  ${warning}`, 'yellow');
    }
  }
  
  log('\n' + '═'.repeat(50), 'blue');
  
  const success = metrics.errors.length === 0;
  log(`Build ${success ? 'SUCCEEDED' : 'FAILED'}`, success ? 'green' : 'red');
  
  return success;
}

function printHelp() {
  console.log(`
Interactive Agent Build Script

Usage: node build-all.js [mode] [options]

Modes:
  development    Development build with source maps (default)
  production     Production build with optimizations
  clean          Clean build artifacts only

Options:
  --verbose         Show detailed output
  --watch           Watch mode for development
  --no-type-check   Skip TypeScript type checking
  --no-source-maps  Disable source map generation
  --no-test         Skip running tests
  --clean           Clean before building
  --help            Show this help message

Examples:
  node build-all.js                    # Development build
  node build-all.js production         # Production build
  node build-all.js development --watch # Development with watch mode
  node build-all.js --clean --verbose  # Clean build with detailed output
`);
}

async function main() {
  if (process.argv.includes('--help')) {
    printHelp();
    process.exit(0);
  }
  
  metrics.startTime = performance.now();
  
  log('🚀 Interactive Agent Build System', 'bright');
  log('═'.repeat(50), 'blue');
  log(`Mode: ${config.mode}`, 'cyan');
  log(`TypeScript: ${config.typeCheck ? 'enabled' : 'disabled'}`, 'cyan');
  log(`Source Maps: ${config.sourceMaps ? 'enabled' : 'disabled'}`, 'cyan');
  log(`Watch Mode: ${config.watch ? 'enabled' : 'disabled'}`, 'cyan');
  
  try {
    let success = true;
    
    // Clean if requested or in clean mode
    if (config.clean || config.mode === 'clean') {
      success = await cleanBuild() && success;
      
      if (config.mode === 'clean') {
        metrics.endTime = performance.now();
        printBuildSummary();
        process.exit(success ? 0 : 1);
      }
    }
    
    // Type checking
    success = await typeCheck() && success;
    
    // TypeScript compilation
    if (success) {
      success = await buildTypeScript() && success;
    }
    
    // Path alias resolution
    if (success && !config.watch) {
      success = await resolvePathAliases() && success;
    }
    
    // Build validation
    if (success && !config.watch) {
      success = await validateBuild() && success;
    }
    
    // Tests
    if (success && !config.watch) {
      await runTests(); // Don't fail build for test failures
    }
    
    metrics.endTime = performance.now();
    
    if (!config.watch) {
      printBuildSummary();
    }
    
    if (config.watch) {
      log('\n👀 Watching for changes...', 'cyan');
      // Keep process alive in watch mode
    } else {
      process.exit(success ? 0 : 1);
    }
    
  } catch (error) {
    metrics.endTime = performance.now();
    log(`\n💥 Build failed with error: ${error.message}`, 'red');
    if (config.verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle SIGINT for graceful shutdown in watch mode
process.on('SIGINT', () => {
  log('\n\n👋 Build process interrupted', 'yellow');
  process.exit(0);
});

// Run the build
main().catch(console.error);
