import { jest } from '@jest/globals';

// Import the retry manager
const { executeWithRetry, calculateDelay, RetryPolicy } = await import('./retry-manager.js');

describe('Retry Manager', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('executeWithRetry', () => {
    it('should execute function successfully on first try', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await executeWithRetry(mockFn);
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on failure and eventually succeed', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('First failure'))
        .mockRejectedValueOnce(new Error('Second failure'))
        .mockResolvedValue('success');
      
      const promise = executeWithRetry(mockFn, { maxRetries: 3 });
      
      // Fast-forward through delays
      await jest.runAllTimersAsync();
      
      const result = await promise;
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should fail after max retries', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Persistent failure'));
      
      const promise = executeWithRetry(mockFn, { maxRetries: 2 });
      
      await jest.runAllTimersAsync();
      
      await expect(promise).rejects.toThrow('Persistent failure');
      expect(mockFn).toHaveBeenCalledTimes(3); // Initial + 2 retries
    });

    it('should respect AbortController signal', async () => {
      const controller = new AbortController();
      const mockFn = jest.fn().mockRejectedValue(new Error('Failure'));
      
      const promise = executeWithRetry(mockFn, { 
        maxRetries: 5,
        signal: controller.signal 
      });
      
      // Abort after first failure
      setTimeout(() => controller.abort(), 100);
      
      await jest.runAllTimersAsync();
      
      await expect(promise).rejects.toThrow('aborted');
    });

    it('should use correct retry policy', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Failure'));
      
      const promise = executeWithRetry(mockFn, { 
        maxRetries: 2,
        policy: RetryPolicy.SPAWN,
      });
      
      await jest.runAllTimersAsync();
      
      await expect(promise).rejects.toThrow('Failure');
      expect(mockFn).toHaveBeenCalledTimes(3);
    });

    it('should handle non-retryable errors', async () => {
      const nonRetryableError = new Error('EACCES: permission denied') as any;
      nonRetryableError.code = 'EACCES';
      
      const mockFn = jest.fn().mockRejectedValue(nonRetryableError);
      
      await expect(executeWithRetry(mockFn)).rejects.toThrow('EACCES');
      expect(mockFn).toHaveBeenCalledTimes(1); // No retries for non-retryable errors
    });

    it('should handle timeout correctly', async () => {
      const mockFn = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 10000))
      );
      
      const promise = executeWithRetry(mockFn, { 
        maxRetries: 1,
        timeout: 1000 
      });
      
      await jest.runAllTimersAsync();
      
      await expect(promise).rejects.toThrow('timeout');
    });
  });

  describe('calculateDelay', () => {
    it('should calculate exponential backoff correctly', () => {
      const policy = RetryPolicy.SPAWN;
      
      const delay1 = calculateDelay(1, policy);
      const delay2 = calculateDelay(2, policy);
      const delay3 = calculateDelay(3, policy);
      
      expect(delay2).toBeGreaterThan(delay1);
      expect(delay3).toBeGreaterThan(delay2);
    });

    it('should include jitter in delay calculation', () => {
      const policy = RetryPolicy.SPAWN;
      
      const delays = [];
      for (let i = 0; i < 10; i++) {
        delays.push(calculateDelay(1, policy));
      }
      
      // With jitter, delays should vary
      const uniqueDelays = new Set(delays);
      expect(uniqueDelays.size).toBeGreaterThan(1);
    });

    it('should respect maximum delay', () => {
      const policy = RetryPolicy.SPAWN;
      
      const delay = calculateDelay(10, policy); // Very high attempt number
      
      expect(delay).toBeLessThanOrEqual(30000); // Max delay should be capped
    });

    it('should handle different retry policies', () => {
      const spawnDelay = calculateDelay(1, RetryPolicy.SPAWN);
      const fileDelay = calculateDelay(1, RetryPolicy.FILE_OPERATION);
      const networkDelay = calculateDelay(1, RetryPolicy.NETWORK);
      
      expect(spawnDelay).toBeGreaterThan(0);
      expect(fileDelay).toBeGreaterThan(0);
      expect(networkDelay).toBeGreaterThan(0);
    });
  });

  describe('Error Classification', () => {
    it('should retry on ENOENT errors', async () => {
      const enoentError = new Error('ENOENT: no such file') as any;
      enoentError.code = 'ENOENT';
      
      const mockFn = jest.fn()
        .mockRejectedValueOnce(enoentError)
        .mockResolvedValue('success');
      
      const promise = executeWithRetry(mockFn, { maxRetries: 1 });
      
      await jest.runAllTimersAsync();
      
      const result = await promise;
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should retry on EBUSY errors', async () => {
      const ebusyError = new Error('EBUSY: resource busy') as any;
      ebusyError.code = 'EBUSY';
      
      const mockFn = jest.fn()
        .mockRejectedValueOnce(ebusyError)
        .mockResolvedValue('success');
      
      const promise = executeWithRetry(mockFn, { maxRetries: 1 });
      
      await jest.runAllTimersAsync();
      
      const result = await promise;
      expect(result).toBe('success');
    });

    it('should not retry on EACCES errors', async () => {
      const eaccesError = new Error('EACCES: permission denied') as any;
      eaccesError.code = 'EACCES';
      
      const mockFn = jest.fn().mockRejectedValue(eaccesError);
      
      await expect(executeWithRetry(mockFn)).rejects.toThrow('EACCES');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should retry on network errors', async () => {
      const networkError = new Error('ECONNRESET') as any;
      networkError.code = 'ECONNRESET';
      
      const mockFn = jest.fn()
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success');
      
      const promise = executeWithRetry(mockFn, { 
        maxRetries: 1,
        policy: RetryPolicy.NETWORK 
      });
      
      await jest.runAllTimersAsync();
      
      const result = await promise;
      expect(result).toBe('success');
    });
  });

  describe('Retry Policies', () => {
    it('should use different base delays for different policies', () => {
      const spawnDelay = calculateDelay(1, RetryPolicy.SPAWN);
      const fileDelay = calculateDelay(1, RetryPolicy.FILE_OPERATION);
      const networkDelay = calculateDelay(1, RetryPolicy.NETWORK);
      
      // Delays should be different for different policies
      expect(spawnDelay).not.toBe(fileDelay);
      expect(fileDelay).not.toBe(networkDelay);
    });

    it('should have appropriate backoff factors', () => {
      const attempt1 = calculateDelay(1, RetryPolicy.SPAWN);
      const attempt2 = calculateDelay(2, RetryPolicy.SPAWN);
      
      // Second attempt should have significantly higher delay
      expect(attempt2).toBeGreaterThan(attempt1 * 1.5);
    });
  });

  describe('Concurrent Retries', () => {
    it('should handle multiple concurrent retry operations', async () => {
      const mockFn1 = jest.fn()
        .mockRejectedValueOnce(new Error('Failure 1'))
        .mockResolvedValue('success 1');
      
      const mockFn2 = jest.fn()
        .mockRejectedValueOnce(new Error('Failure 2'))
        .mockResolvedValue('success 2');
      
      const promises = [
        executeWithRetry(mockFn1, { maxRetries: 1 }),
        executeWithRetry(mockFn2, { maxRetries: 1 }),
      ];
      
      await jest.runAllTimersAsync();
      
      const results = await Promise.all(promises);
      
      expect(results).toEqual(['success 1', 'success 2']);
      expect(mockFn1).toHaveBeenCalledTimes(2);
      expect(mockFn2).toHaveBeenCalledTimes(2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle zero retries', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Failure'));
      
      await expect(executeWithRetry(mockFn, { maxRetries: 0 }))
        .rejects.toThrow('Failure');
      
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should handle negative retries', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Failure'));
      
      await expect(executeWithRetry(mockFn, { maxRetries: -1 }))
        .rejects.toThrow('Failure');
      
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should handle very large retry counts', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const result = await executeWithRetry(mockFn, { maxRetries: 1000 });
      
      expect(result).toBe('success');
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should handle functions that throw synchronously', async () => {
      const mockFn = jest.fn().mockImplementation(() => {
        throw new Error('Sync error');
      });
      
      await expect(executeWithRetry(mockFn, { maxRetries: 1 }))
        .rejects.toThrow('Sync error');
    });
  });

  describe('Performance', () => {
    it('should not add significant overhead for successful operations', async () => {
      const mockFn = jest.fn().mockResolvedValue('success');
      
      const startTime = Date.now();
      await executeWithRetry(mockFn);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(100); // Should be very fast
    });

    it('should respect delay timing for retries', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('Failure'))
        .mockResolvedValue('success');
      
      const startTime = Date.now();
      const promise = executeWithRetry(mockFn, { 
        maxRetries: 1,
        policy: RetryPolicy.SPAWN 
      });
      
      await jest.runAllTimersAsync();
      await promise;
      
      // Should have waited for the delay
      expect(jest.getTimerCount()).toBe(0); // All timers should be cleared
    });
  });

  describe('Error Aggregation', () => {
    it('should include information from all attempts in final error', async () => {
      const mockFn = jest.fn()
        .mockRejectedValueOnce(new Error('First error'))
        .mockRejectedValueOnce(new Error('Second error'))
        .mockRejectedValue(new Error('Final error'));
      
      const promise = executeWithRetry(mockFn, { maxRetries: 2 });
      
      await jest.runAllTimersAsync();
      
      try {
        await promise;
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Final error');
      }
    });
  });
});
