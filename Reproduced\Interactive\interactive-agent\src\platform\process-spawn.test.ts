import { jest } from '@jest/globals';
import { EventEmitter } from 'events';

// Mock child process
class MockChildProcess extends EventEmitter {
  pid = 12345;
  stdout = new EventEmitter();
  stderr = new EventEmitter();
  stdin = { write: jest.fn(), end: jest.fn() };
  
  kill = jest.fn();
  disconnect = jest.fn();
}

// Mock modules
const mockChildProcess = {
  spawn: jest.fn(),
};

const mockPlatformDetector = {
  getPlatformInfo: jest.fn(),
};

const mockRetryManager = {
  executeWithRetry: jest.fn(),
};

const mockOrphanManager = {
  registerProcess: jest.fn(),
  cleanup: jest.fn(),
};

// Mock modules before importing
jest.unstable_mockModule('child_process', () => mockChildProcess);
jest.unstable_mockModule('./platform-detector.js', () => mockPlatformDetector);
jest.unstable_mockModule('../utils/retry-manager.js', () => mockRetryManager);
jest.unstable_mockModule('../utils/orphan-manager.js', () => mockOrphanManager);

// Import after mocking
const { spawnInTerminal, spawnWithRetry } = await import('./process-spawn.js');

describe('Process Spawn', () => {
  let mockProcess: MockChildProcess;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockProcess = new MockChildProcess();
    mockChildProcess.spawn.mockReturnValue(mockProcess);
    
    // Default platform info
    mockPlatformDetector.getPlatformInfo.mockResolvedValue({
      platform: 'linux',
      isWSL: false,
      isMSYS: false,
      shell: '/bin/bash',
      hasDisplay: true,
      isHeadless: false,
      terminals: ['gnome-terminal', 'xterm'],
    });

    // Default retry behavior - just execute the function
    mockRetryManager.executeWithRetry.mockImplementation(async (fn: Function) => {
      return await fn();
    });
  });

  describe('spawnInTerminal', () => {
    it('should spawn a process successfully', async () => {
      const command = 'echo "hello"';
      const options = { cwd: '/tmp' };

      // Simulate successful process
      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await spawnInTerminal(command, options);

      expect(mockChildProcess.spawn).toHaveBeenCalled();
      expect(mockOrphanManager.registerProcess).toHaveBeenCalledWith(mockProcess.pid, expect.any(String));
      expect(result.success).toBe(true);
      expect(result.exitCode).toBe(0);
    });

    it('should handle process spawn errors', async () => {
      const command = 'invalid-command';

      // Simulate spawn error
      setTimeout(() => {
        mockProcess.emit('error', new Error('ENOENT'));
      }, 10);

      const result = await spawnInTerminal(command);

      expect(result.success).toBe(false);
      expect(result.error).toContain('ENOENT');
    });

    it('should handle process timeout', async () => {
      const command = 'sleep 10';
      const options = { timeout: 100 };

      // Don't emit any events to simulate hanging process
      const result = await spawnInTerminal(command, options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('timeout');
      expect(mockProcess.kill).toHaveBeenCalled();
    });

    it('should use correct terminal for Linux', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'linux',
        terminals: ['gnome-terminal'],
        shell: '/bin/bash',
        hasDisplay: true,
        isHeadless: false,
        isWSL: false,
        isMSYS: false,
      });

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal('echo test');

      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      expect(spawnCall[0]).toBe('gnome-terminal');
      expect(spawnCall[1]).toContain('--');
    });

    it('should use correct terminal for Windows', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'win32',
        terminals: ['cmd.exe'],
        shell: 'cmd.exe',
        hasDisplay: true,
        isHeadless: false,
        isWSL: false,
        isMSYS: false,
      });

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal('echo test');

      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      expect(spawnCall[0]).toBe('cmd.exe');
      expect(spawnCall[1]).toContain('/c');
    });

    it('should use correct terminal for macOS', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'darwin',
        terminals: ['Terminal.app'],
        shell: '/bin/zsh',
        hasDisplay: true,
        isHeadless: false,
        isWSL: false,
        isMSYS: false,
      });

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal('echo test');

      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      expect(spawnCall[0]).toBe('open');
      expect(spawnCall[1]).toContain('-a');
      expect(spawnCall[1]).toContain('Terminal');
    });

    it('should handle headless environment', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'linux',
        terminals: [],
        shell: '/bin/bash',
        hasDisplay: false,
        isHeadless: true,
        isWSL: false,
        isMSYS: false,
      });

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal('echo test');

      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      expect(spawnCall[0]).toBe('/bin/bash');
      expect(spawnCall[1]).toContain('-c');
    });

    it('should escape command arguments properly', async () => {
      const command = 'echo "hello world" && ls';

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal(command);

      const spawnCall = mockChildProcess.spawn.mock.calls[0];
      const commandArg = spawnCall[1].find((arg: string) => arg.includes('echo'));
      expect(commandArg).toBeDefined();
    });

    it('should set working directory correctly', async () => {
      const options = { cwd: '/custom/path' };

      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      await spawnInTerminal('pwd', options);

      const spawnOptions = mockChildProcess.spawn.mock.calls[0][2];
      expect(spawnOptions.cwd).toBe('/custom/path');
    });

    it('should handle AbortController cancellation', async () => {
      const controller = new AbortController();
      const options = { signal: controller.signal };

      // Cancel immediately
      controller.abort();

      const result = await spawnInTerminal('echo test', options);

      expect(result.success).toBe(false);
      expect(result.error).toContain('aborted');
    });
  });

  describe('spawnWithRetry', () => {
    it('should use retry manager for spawn operations', async () => {
      const command = 'echo test';
      
      mockRetryManager.executeWithRetry.mockResolvedValue({
        success: true,
        exitCode: 0,
        sessionId: 'test-session',
      });

      const result = await spawnWithRetry(command);

      expect(mockRetryManager.executeWithRetry).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('should pass retry options to retry manager', async () => {
      const command = 'echo test';
      const retryOptions = { maxRetries: 5, baseDelay: 1000 };

      await spawnWithRetry(command, {}, retryOptions);

      expect(mockRetryManager.executeWithRetry).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining(retryOptions)
      );
    });

    it('should handle retry failures', async () => {
      const command = 'failing-command';
      
      mockRetryManager.executeWithRetry.mockRejectedValue(new Error('Max retries exceeded'));

      await expect(spawnWithRetry(command)).rejects.toThrow('Max retries exceeded');
    });
  });

  describe('Error Handling', () => {
    it('should handle ENOENT errors gracefully', async () => {
      setTimeout(() => {
        const error = new Error('spawn ENOENT') as any;
        error.code = 'ENOENT';
        mockProcess.emit('error', error);
      }, 10);

      const result = await spawnInTerminal('nonexistent-command');

      expect(result.success).toBe(false);
      expect(result.error).toContain('ENOENT');
    });

    it('should handle EACCES errors gracefully', async () => {
      setTimeout(() => {
        const error = new Error('spawn EACCES') as any;
        error.code = 'EACCES';
        mockProcess.emit('error', error);
      }, 10);

      const result = await spawnInTerminal('/root/restricted-command');

      expect(result.success).toBe(false);
      expect(result.error).toContain('EACCES');
    });

    it('should handle platform detection failures', async () => {
      mockPlatformDetector.getPlatformInfo.mockRejectedValue(new Error('Platform detection failed'));

      const result = await spawnInTerminal('echo test');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Platform detection failed');
    });
  });

  describe('Session Management', () => {
    it('should generate unique session IDs', async () => {
      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      const result1 = await spawnInTerminal('echo test1');
      const result2 = await spawnInTerminal('echo test2');

      expect(result1.sessionId).toBeDefined();
      expect(result2.sessionId).toBeDefined();
      expect(result1.sessionId).not.toBe(result2.sessionId);
    });

    it('should track session in orphan manager', async () => {
      setTimeout(() => {
        mockProcess.emit('spawn');
        mockProcess.emit('close', 0);
      }, 10);

      const result = await spawnInTerminal('echo test');

      expect(mockOrphanManager.registerProcess).toHaveBeenCalledWith(
        mockProcess.pid,
        result.sessionId
      );
    });
  });

  describe('Terminal Fallback', () => {
    it('should fallback to next available terminal', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'linux',
        terminals: ['nonexistent-terminal', 'xterm'],
        shell: '/bin/bash',
        hasDisplay: true,
        isHeadless: false,
        isWSL: false,
        isMSYS: false,
      });

      // First spawn fails, second succeeds
      mockChildProcess.spawn
        .mockReturnValueOnce((() => {
          const failProcess = new MockChildProcess();
          setTimeout(() => failProcess.emit('error', new Error('ENOENT')), 10);
          return failProcess;
        })())
        .mockReturnValueOnce((() => {
          const successProcess = new MockChildProcess();
          setTimeout(() => {
            successProcess.emit('spawn');
            successProcess.emit('close', 0);
          }, 10);
          return successProcess;
        })());

      const result = await spawnInTerminal('echo test');

      expect(mockChildProcess.spawn).toHaveBeenCalledTimes(2);
      expect(result.success).toBe(true);
    });

    it('should fail when all terminals are unavailable', async () => {
      mockPlatformDetector.getPlatformInfo.mockResolvedValue({
        platform: 'linux',
        terminals: ['nonexistent1', 'nonexistent2'],
        shell: '/bin/bash',
        hasDisplay: true,
        isHeadless: false,
        isWSL: false,
        isMSYS: false,
      });

      // All spawns fail
      mockChildProcess.spawn.mockImplementation(() => {
        const failProcess = new MockChildProcess();
        setTimeout(() => failProcess.emit('error', new Error('ENOENT')), 10);
        return failProcess;
      });

      const result = await spawnInTerminal('echo test');

      expect(result.success).toBe(false);
      expect(result.error).toContain('No available terminal');
    });
  });
});
