{"version": 3, "file": "orphan-manager.js", "sourceRoot": "", "sources": ["../../src/utils/orphan-manager.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,+BAA+B,EAAE,wBAAwB,EAAE,uBAAuB,EAAE,MAAM,iBAAiB,CAAC;AAarH;;GAEG;AACH,MAAM,OAAO,aAAa;IAChB,MAAM,CAAC,QAAQ,GAAyB,IAAI,CAAC;IACpC,SAAS,GAAG,IAAI,GAAG,EAAuB,CAAC;IAC3C,mBAAmB,GAAG,GAAG,CAAC;IACnC,kBAAkB,GAA0B,IAAI,CAAC;IACjD,wBAAwB,GAAG,KAAK,CAAC;IAEzC;QACE,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC5B,aAAa,CAAC,QAAQ,GAAG,IAAI,aAAa,EAAE,CAAC;QAC/C,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,OAAqB,EAAE,SAAkB;QACpE,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QAED,wBAAwB;QACxB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC,0BAA0B,IAAI,CAAC,mBAAmB,6BAA6B,CAAC,CAAC;YAC9F,MAAM,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;QAC1D,CAAC;QAED,MAAM,WAAW,GAAgB;YAC/B,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,aAAa,EAAE,IAAI,IAAI,EAAE;YACzB,SAAS;SACV,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAE7C,uDAAuD;QACvD,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAI,CAAC,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,KAAK,CAAC,sBAAsB,OAAO,CAAC,GAAG,eAAe,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACI,iBAAiB,CAAC,GAAW;QAClC,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO,CAAC,KAAK,CAAC,wBAAwB,GAAG,gBAAgB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,GAAW;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,GAAW;QACnC,IAAI,CAAC;YACH,8EAA8E;YAC9E,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,oDAAoD;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,SAAiB;QACzC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,cAAc,GAAkB,EAAE,CAAC;QAEzC,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,MAAM,kBAAkB,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,WAAW,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAE/E,IAAI,kBAAkB,GAAG,SAAS,EAAE,CAAC;gBACnC,2CAA2C;gBAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC9C,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACnC,CAAC;qBAAM,CAAC;oBACN,qCAAqC;oBACrC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,cAAc,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,GAAW,EAAE,SAAyB,SAAS;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,wBAAwB,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,CAAC,8BAA8B,GAAG,SAAS,MAAM,EAAE,CAAC,CAAC;YAElE,iCAAiC;YACjC,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;gBACrB,gDAAgD;gBAChD,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,gBAAgB;gBAChB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;gBAE1B,mCAAmC;gBACnC,MAAM,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAE1C,6BAA6B;gBAC7B,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjC,OAAO,CAAC,KAAK,CAAC,WAAW,GAAG,6BAA6B,CAAC,CAAC;oBAC3D,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,IAAI,CAAC,0BAA0B,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC;YACtD,4CAA4C;YAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB;QAC3B,OAAO,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,IAAI,oBAAoB,CAAC,CAAC;QAEtE,MAAM,YAAY,GAAuB,EAAE,CAAC;QAC5C,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAClD,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC;QACvD,CAAC;QAED,MAAM,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACvC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;QAE1D,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,SAAS,cAAc,CAAC,MAAM,8BAA8B,CAAC,CAAC;YAE5E,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CACpD,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,CAClC,CAAC;YAEF,MAAM,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,cAAc,CAAC,MAAM,CAAC;IAC/B,CAAC;IAED;;OAEG;IACI,mBAAmB;QACxB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,eAAe;QACpB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,SAAS;QACf,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,GAAW;QAC1C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;QAEhD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;YAEnE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBAC3B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,OAAO,EAAE,CAAC;gBACZ,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,KAAK,CAAC,6BAA6B,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzD,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,sBAAsB;QAC5B,IAAI,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;YACzB,OAAO,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAChC,CAAC,CAAC;QAEF,8CAA8C;QAC9C,IAAI,SAAS,GAAG,KAAK,CAAC;QAEtB,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,IAAI,SAAS;gBAAE,OAAO;YACtB,SAAS,GAAG,IAAI,CAAC;YAEjB,0DAA0D;YAC1D,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClD,IAAI,CAAC;oBACH,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;wBACrB,iDAAiD;wBACjD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC3C,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,6CAA6C;gBAC/C,CAAC;YACH,CAAC;YAED,+BAA+B;YAC/B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACjC,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAEvC,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,EAAE;YAC1C,IAAI,SAAS;gBAAE,OAAO;YAEtB,OAAO,CAAC,KAAK,CAAC,2BAA2B,MAAM,gCAAgC,CAAC,CAAC;YAEjF,mCAAmC;YACnC,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;gBACtC,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;gBACtE,kBAAkB,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,EAAE,uBAAuB,CAAC,CAAC;YAE5B,wCAAwC;YACxC,OAAO,EAAE;iBACN,IAAI,CAAC,GAAG,EAAE;gBACT,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,gDAAgD,EAAE,KAAK,CAAC,CAAC;gBACtE,YAAY,CAAC,eAAe,CAAC,CAAC;gBAC9B,kBAAkB,EAAE,CAAC;gBACrB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACvD,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;QAEzD,mBAAmB;QACnB,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACrB,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;gBAChC,MAAM,OAAO,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;IACvC,CAAC;IAED;;OAEG;IACK,2BAA2B;QACjC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,OAAO;QACT,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YAC/C,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,EAAE,+BAA+B,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,0BAA0B;QAC/B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;YAC/B,OAAO,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;;AAGH,4BAA4B;AAC5B,MAAM,CAAC,MAAM,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC"}