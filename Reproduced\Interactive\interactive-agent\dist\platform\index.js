// Re-exports from platform-detector
export { getPlatformInfo, getPlatformInfoAsync, refreshPlatformCache, refreshPlatformCacheAsync, getTerminalCapabilities, getTerminalCapabilitiesAsync } from './platform-detector.js';
// Re-exports from process-spawn
export { spawnInTerminal, spawnWithRetry, testTerminalSpawn } from './process-spawn.js';
// Re-exports from terminal-fallbacks
export { TerminalFallbacks } from './terminal-fallbacks.js';
// Convenience functions and initialization
import { getPlatformInfo } from './platform-detector.js';
import { spawnInTerminal, spawnWithRetry } from './process-spawn.js';
import { TerminalFallbacks } from './terminal-fallbacks.js';
/**
 * Initialize platform module with orphan manager and signal handlers
 */
export function initializePlatform() {
    // Orphan manager is automatically initialized as singleton
    console.debug('Platform module initialized with orphan tracking');
}
/**
 * Check if terminal spawning is possible in current environment
 */
export async function canSpawnTerminal() {
    return await TerminalFallbacks.canSpawnTerminal();
}
/**
 * Find the best terminal for current environment
 */
export async function findBestTerminal() {
    return await TerminalFallbacks.findBestTerminal();
}
/**
 * Get available terminals for current platform
 */
export async function getAvailableTerminals() {
    return await TerminalFallbacks.getAvailableTerminals();
}
/**
 * Detect preferred shell for current platform
 */
export function detectShell() {
    const platformInfo = getPlatformInfo();
    return platformInfo.shell;
}
/**
 * Enhanced spawn with automatic fallback and retry logic
 */
export async function spawnWithFallback(command, args = [], options = {}) {
    // Try enhanced spawn with retry first
    const result = await spawnWithRetry(command, args, options);
    if (result.success) {
        return { ...result, usedFallback: false };
    }
    // Check fallback strategy
    const fallbackStrategy = TerminalFallbacks.getFallbackStrategy();
    if (fallbackStrategy === 'console') {
        return {
            process: null,
            success: false,
            error: 'Terminal spawning failed, console fallback recommended',
            usedFallback: true
        };
    }
    // Try to find alternative terminals
    const availableTerminals = await TerminalFallbacks.getAvailableTerminals();
    const workingTerminals = availableTerminals.filter(t => t.available);
    for (const terminal of workingTerminals) {
        try {
            const fallbackResult = spawnInTerminal(command, args, options);
            if (fallbackResult.success) {
                return { ...fallbackResult, usedFallback: true };
            }
        }
        catch (error) {
            continue;
        }
    }
    return {
        process: null,
        success: false,
        error: 'All terminal spawn attempts failed',
        usedFallback: true
    };
}
/**
 * Obtient le type de plateforme simplifié
 */
export function getPlatformType() {
    const platformInfo = getPlatformInfo();
    switch (platformInfo.platform) {
        case 'win32':
            return 'win32';
        case 'darwin':
            return 'darwin';
        case 'linux':
            return 'linux';
        default:
            return 'other';
    }
}
/**
 * Vérifie si la plateforme est Windows (incluant WSL)
 */
export function isWindows() {
    const platformInfo = getPlatformInfo();
    return platformInfo.platform === 'win32' || platformInfo.isWSL;
}
/**
 * Vérifie si la plateforme est macOS
 */
export function isMacOS() {
    const platformInfo = getPlatformInfo();
    return platformInfo.platform === 'darwin';
}
/**
 * Vérifie si la plateforme est Linux (excluant WSL)
 */
export function isLinux() {
    const platformInfo = getPlatformInfo();
    return platformInfo.platform === 'linux' && !platformInfo.isWSL;
}
/**
 * Vérifie si nous sommes dans WSL
 */
export function isWSL() {
    const platformInfo = getPlatformInfo();
    return platformInfo.isWSL;
}
export function getPlatformSummary() {
    const platformInfo = getPlatformInfo();
    return {
        type: getPlatformType(),
        canSpawnTerminal: platformInfo.canSpawnTerminal,
        hasDisplay: platformInfo.hasDisplay,
        isHeadless: platformInfo.isHeadless,
        preferredShell: platformInfo.shell,
        terminalCount: platformInfo.availableTerminals.length
    };
}
// Auto-initialize platform module when imported
initializePlatform();
//# sourceMappingURL=index.js.map