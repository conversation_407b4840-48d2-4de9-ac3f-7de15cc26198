#!/usr/bin/env node

/**
 * Enhanced platform testing script for Interactive Agent
 * Tests platform detection, terminal capabilities, and process management
 */

import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const projectRoot = join(__dirname);

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Configuration
const config = {
  interactive: process.argv.includes('--interactive'),
  verbose: process.argv.includes('--verbose'),
  safe: !process.argv.includes('--unsafe'),
  json: process.argv.includes('--json'),
};

function log(message, color = 'reset') {
  if (!config.json) {
    console.log(`${colors[color]}${message}${colors.reset}`);
  }
}

function logVerbose(message, color = 'reset') {
  if (config.verbose && !config.json) {
    console.log(`${colors[color]}  ${message}${colors.reset}`);
  }
}

// Check if build exists
function checkBuild() {
  const distPath = join(projectRoot, 'dist');
  if (!existsSync(distPath)) {
    log('❌ Build not found. Please run "npm run build" first.', 'red');
    process.exit(1);
  }
  return true;
}

log('🚀 Interactive Agent Platform Testing Suite', 'bright');
log('═'.repeat(50), 'blue');

async function testPlatformDetection() {
  log('\n🖥️  Testing Platform Detection...', 'cyan');

  try {
    checkBuild();
    const { getPlatformInfo } = await import('./dist/platform/platform-detector.js');

    const startTime = Date.now();
    const platformInfo = await getPlatformInfo();
    const endTime = Date.now();

    log('✅ Platform detection successful', 'green');
    logVerbose(`Detection time: ${endTime - startTime}ms`, 'blue');

    log('\n📋 Platform Information:', 'bright');
    log(`Platform: ${platformInfo.platform}`, 'blue');
    log(`WSL: ${platformInfo.isWSL}`, 'blue');
    log(`MSYS: ${platformInfo.isMSYS}`, 'blue');
    log(`Shell: ${platformInfo.shell}`, 'blue');
    log(`Has Display: ${platformInfo.hasDisplay}`, 'blue');
    log(`Is Headless: ${platformInfo.isHeadless}`, 'blue');
    log(`Terminals: ${platformInfo.terminals.join(', ')}`, 'blue');

    return { success: true, platformInfo, detectionTime: endTime - startTime };
  } catch (error) {
    log(`❌ Platform detection failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function testProcessSpawning() {
  log('\n🔄 Testing Process Spawning...', 'cyan');

  try {
    const { spawnInTerminal } = await import('./dist/platform/process-spawn.js');

    if (!config.interactive) {
      log('⚠️  Skipping interactive spawn tests (use --interactive to enable)', 'yellow');
      return { success: true, skipped: true };
    }

    if (config.safe) {
      log('🛡️  Running safe spawn test...', 'yellow');

      // Test with a safe, quick command
      const testCommand = process.platform === 'win32' ? 'echo "Test successful"' : 'echo "Test successful"';

      const startTime = Date.now();
      const result = await spawnInTerminal(testCommand, { timeout: 5000 });
      const endTime = Date.now();

      if (result.success) {
        log('✅ Process spawning successful', 'green');
        logVerbose(`Spawn time: ${endTime - startTime}ms`, 'blue');
        logVerbose(`Session ID: ${result.sessionId}`, 'blue');
        logVerbose(`Exit code: ${result.exitCode}`, 'blue');
      } else {
        log(`❌ Process spawning failed: ${result.error}`, 'red');
      }

      return { success: result.success, spawnTime: endTime - startTime };
    } else {
      log('⚠️  Unsafe mode not implemented in enhanced version', 'yellow');
      return { success: true, skipped: true };
    }
  } catch (error) {
    log(`❌ Process spawning test failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function testFileOperations() {
  log('\n📁 Testing File Operations...', 'cyan');

  try {
    const { createTempFile, createTempDir, cleanupTempFiles } = await import('./dist/utils/temp-file-manager.js');

    const startTime = Date.now();

    // Test temp file creation
    const tempFile = await createTempFile('Test content for Interactive Agent', '.txt');
    const fileTime = Date.now();

    // Test temp directory creation
    const tempDir = await createTempDir('test-session');
    const dirTime = Date.now();

    // Test cleanup
    await cleanupTempFiles();
    const cleanupTime = Date.now();

    log('✅ File operations successful', 'green');
    logVerbose(`File creation: ${fileTime - startTime}ms`, 'blue');
    logVerbose(`Directory creation: ${dirTime - fileTime}ms`, 'blue');
    logVerbose(`Cleanup: ${cleanupTime - dirTime}ms`, 'blue');
    logVerbose(`Temp file: ${tempFile}`, 'blue');
    logVerbose(`Temp dir: ${tempDir}`, 'blue');

    return {
      success: true,
      fileCreationTime: fileTime - startTime,
      dirCreationTime: dirTime - fileTime,
      cleanupTime: cleanupTime - dirTime,
    };
  } catch (error) {
    log(`❌ File operations test failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

async function testRetryLogic() {
  log('\n🔄 Testing Retry Logic...', 'cyan');

  try {
    const { executeWithRetry } = await import('./dist/utils/retry-manager.js');

    // Test successful operation
    let callCount = 0;
    const successFn = async () => {
      callCount++;
      return 'success';
    };

    const startTime = Date.now();
    const result = await executeWithRetry(successFn);
    const successTime = Date.now();

    if (result === 'success' && callCount === 1) {
      log('✅ Retry logic working correctly', 'green');
      logVerbose(`Success operation: ${successTime - startTime}ms`, 'blue');
      logVerbose(`Call count: ${callCount}`, 'blue');
    } else {
      log(`❌ Unexpected retry behavior: result=${result}, calls=${callCount}`, 'red');
    }

    return { success: true, operationTime: successTime - startTime };
  } catch (error) {
    log(`❌ Retry logic test failed: ${error.message}`, 'red');
    return { success: false, error: error.message };
  }
}

function printSummary(results) {
  if (config.json) {
    console.log(JSON.stringify(results, null, 2));
    return;
  }

  log('\n📊 Test Summary', 'bright');
  log('═'.repeat(50), 'blue');

  let totalTests = 0;
  let passedTests = 0;
  let skippedTests = 0;

  for (const [testName, result] of Object.entries(results)) {
    totalTests++;
    if (result.success) {
      if (result.skipped) {
        skippedTests++;
        log(`⚠️  ${testName}: SKIPPED`, 'yellow');
      } else {
        passedTests++;
        log(`✅ ${testName}: PASSED`, 'green');
      }
    } else {
      log(`❌ ${testName}: FAILED - ${result.error}`, 'red');
    }
  }

  log('\n' + '═'.repeat(50), 'blue');
  log(`Total: ${totalTests} | Passed: ${passedTests} | Skipped: ${skippedTests} | Failed: ${totalTests - passedTests - skippedTests}`, 'blue');

  const success = (totalTests - skippedTests) === passedTests;
  log(`\nOverall: ${success ? 'SUCCESS' : 'FAILURE'}`, success ? 'green' : 'red');

  return success;
}

function printHelp() {
  console.log(`
Interactive Agent Platform Test Suite

Usage: node test-platform.js [options]

Options:
  --interactive    Enable interactive tests (may open terminal windows)
  --verbose        Show detailed output
  --unsafe         Enable potentially unsafe tests
  --json           Output results in JSON format
  --help           Show this help message

Examples:
  node test-platform.js                    # Basic platform tests
  node test-platform.js --verbose          # Detailed output
  node test-platform.js --interactive      # Include terminal spawn tests
  node test-platform.js --json             # JSON output for automation
`);
}

async function main() {
  if (process.argv.includes('--help')) {
    printHelp();
    process.exit(0);
  }

  try {
    const results = {};

    // Run all tests
    results.platformDetection = await testPlatformDetection();
    results.processSpawning = await testProcessSpawning();
    results.fileOperations = await testFileOperations();
    results.retryLogic = await testRetryLogic();

    // Print summary
    const success = printSummary(results);

    process.exit(success ? 0 : 1);
  } catch (error) {
    if (config.json) {
      console.error(JSON.stringify({ error: error.message }, null, 2));
    } else {
      log(`\n💥 Test suite failed: ${error.message}`, 'red');
      if (config.verbose) {
        console.error(error.stack);
      }
    }
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the tests
main().catch(console.error);
