{"version": 3, "file": "retry-manager.js", "sourceRoot": "", "sources": ["../../src/utils/retry-manager.ts"], "names": [], "mappings": "AAWA;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACnC,eAAe;IACf,MAAM,EAAE,QAAQ,EAAM,oBAAoB;IAC1C,MAAM,EAAE,QAAQ,EAAM,oBAAoB;IAC1C,MAAM,EAAE,QAAQ,EAAM,sBAAsB;IAC5C,MAAM,EAAE,QAAQ,EAAM,sBAAsB;IAC5C,MAAM,EAAE,QAAQ,EAAM,mCAAmC;IAEzD,iBAAiB;IACjB,YAAY,EAAE,cAAc;IAC5B,aAAa,EAAE,eAAe;IAE9B,mBAAmB;IACnB,iBAAiB,EAAE,mBAAmB;IAEtC,wBAAwB;IACxB,KAAK,EAAE,OAAO,EAAQ,gBAAgB;IACtC,SAAS,EAAE,WAAW,EAAE,sBAAsB;CACtC,CAAC;AAEX;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG;IAC5B,kBAAkB,EAAE;QAClB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,KAAK;QACjB,aAAa,EAAE,GAAG;QAClB,eAAe,EAAE;YACf,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,MAAM;YAC5B,qBAAqB,CAAC,YAAY;YAClC,qBAAqB,CAAC,aAAa;SACpC;KACF;IAED,2BAA2B,EAAE;QAC3B,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,GAAG;QAChB,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE,GAAG;QAClB,eAAe,EAAE;YACf,qBAAqB,CAAC,KAAK;YAC3B,qBAAqB,CAAC,SAAS;YAC/B,qBAAqB,CAAC,MAAM;SAC7B;KACF;IAED,oBAAoB,EAAE;QACpB,UAAU,EAAE,CAAC;QACb,WAAW,EAAE,IAAI;QACjB,UAAU,EAAE,KAAK;QACjB,aAAa,EAAE,GAAG;QAClB,eAAe,EAAE;YACf,YAAY;YACZ,cAAc;YACd,WAAW;YACX,WAAW;SACZ;KACF;CACO,CAAC;AAyBX;;GAEG;AACH,MAAM,OAAO,YAAY;IACvB;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,MAAmB;QAC/D,MAAM,gBAAgB,GAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;QAC1F,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;QAElE,6CAA6C;QAC7C,MAAM,MAAM,GAAG,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,GAAG,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,gBAAgB,CAAC,KAAY,EAAE,MAAmB;QAC9D,MAAM,SAAS,GAAI,KAAa,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC;QACrE,OAAO,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CACjD,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAC3E,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAClC,SAAoD,EACpD,MAAmB,EACnB,WAAyB;QAEzB,MAAM,MAAM,GAAY,EAAE,CAAC;QAC3B,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,SAA4B,CAAC;QAEjC,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,OAAO,EAAE,EAAE,CAAC;YAClE,IAAI,CAAC;gBACH,iCAAiC;gBACjC,IAAI,WAAW,EAAE,OAAO,EAAE,CAAC;oBACzB,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;gBACvC,CAAC;gBAED,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,CAAC;gBAE5C,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM;oBACN,QAAQ,EAAE,OAAO;oBACjB,UAAU;oBACV,MAAM;iBACP,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,GAAG,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACtE,SAAS,GAAG,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEjB,MAAM,aAAa,GAAG,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC;gBAElD,oBAAoB;gBACpB,OAAO,CAAC,IAAI,CAAC,iBAAiB,OAAO,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC,UAAU,EAAE;oBACxE,KAAK,EAAE,GAAG,CAAC,OAAO;oBAClB,IAAI,EAAG,GAAW,CAAC,IAAI;oBACvB,aAAa;oBACb,SAAS,EAAE,CAAC,aAAa,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC;iBAChE,CAAC,CAAC;gBAEH,8DAA8D;gBAC9D,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,CAAC;oBACzD,MAAM;gBACR,CAAC;gBAED,4BAA4B;gBAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACnD,UAAU,IAAI,KAAK,CAAC;gBAEpB,gDAAgD;gBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,uDAAuD;QACvD,MAAM,eAAe,GAAG,IAAI,KAAK,CAC/B,0BAA0B,MAAM,CAAC,MAAM,0BAA0B,SAAS,EAAE,OAAO,EAAE,CACtF,CAAC;QACD,eAAuB,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;QACjD,eAAuB,CAAC,MAAM,GAAG,MAAM,CAAC;QACxC,eAAuB,CAAC,UAAU,GAAG,UAAU,CAAC;QAEjD,OAAO;YACL,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,eAAe;YACtB,QAAQ,EAAE,MAAM,CAAC,MAAM;YACvB,UAAU;YACV,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAU,EAAE,WAAyB;QAC9D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,WAAW,EAAE,OAAO,EAAE,CAAC;gBACzB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBACvC,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAExC,MAAM,YAAY,GAAG,GAAG,EAAE;gBACxB,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACzC,CAAC,CAAC;YAEF,WAAW,EAAE,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAErE,2CAA2C;YAC3C,UAAU,CAAC,GAAG,EAAE;gBACd,WAAW,EAAE,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YAC1D,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,YAAY,CACxB,UAAuB,EACvB,SAA+B;QAE/B,OAAO;YACL,GAAG,UAAU;YACb,GAAG,SAAS;YACZ,eAAe,EAAE,SAAS,CAAC,eAAe,IAAI,UAAU,CAAC,eAAe;SACzE,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CACrB,EAA8B,EAC9B,MAAmB;QAEnB,OAAO,KAAK,EAAE,GAAG,IAAO,EAAc,EAAE;YACtC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CACxC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EACjB,MAAM,CACP,CAAC;YAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,MAAO,CAAC;YACxB,CAAC;iBAAM,CAAC;gBACN,MAAM,MAAM,CAAC,KAAK,CAAC;YACrB,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;CACF"}