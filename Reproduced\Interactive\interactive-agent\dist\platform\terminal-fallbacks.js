import { spawn } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { getPlatformInfo } from './platform-detector.js';
import { RetryManager, RETRY_POLICIES } from '../utils/retry-manager.js';
import { TERMINAL_TEST_TIMEOUT_MS, WINDOWS_TERMINAL_TEST_TIMEOUT_MS } from '../constants.js';
/**
 * Terminal detection and fallback system
 */
export class TerminalFallbacks {
    static terminalCache = new Map();
    static environmentInfo = null;
    /**
     * Terminal preferences by platform with priority ordering
     */
    static TERMINAL_PREFERENCES = {
        linux: [
            { name: 'gnome-terminal', command: 'gnome-terminal', priority: 10 },
            { name: 'konsole', command: 'konsole', priority: 9 },
            { name: 'xfce4-terminal', command: 'xfce4-terminal', priority: 8 },
            { name: 'mate-terminal', command: 'mate-terminal', priority: 7 },
            { name: 'xterm', command: 'xterm', priority: 6 },
            { name: 'urxvt', command: 'urxvt', priority: 5 },
            { name: 'rxvt', command: 'rxvt', priority: 4 },
            { name: 'terminator', command: 'terminator', priority: 3 }
        ],
        darwin: [
            { name: 'Terminal.app', command: 'osascript', priority: 10 },
            { name: 'iTerm2', command: 'osascript', priority: 9 },
            { name: 'Terminal', command: 'open', priority: 8 }
        ],
        win32: [
            { name: 'Windows Terminal', command: 'wt.exe', priority: 10 },
            { name: 'ConEmu', command: 'ConEmu64.exe', priority: 9 },
            { name: 'ConEmu32', command: 'ConEmu.exe', priority: 8 },
            { name: 'Cmder', command: 'cmder.exe', priority: 7 },
            { name: 'Command Prompt', command: 'cmd.exe', priority: 6 }
        ]
    };
    /**
     * Detect current environment characteristics
     */
    static detectEnvironment() {
        if (this.environmentInfo) {
            return this.environmentInfo;
        }
        const env = process.env;
        const isSSH = !!(env.SSH_CLIENT || env.SSH_TTY || env.SSH_CONNECTION);
        const isDocker = fs.existsSync('/.dockerenv') || fs.existsSync('/proc/1/cgroup') &&
            fs.readFileSync('/proc/1/cgroup', 'utf8').includes('docker');
        // Common CI environment variables
        const ciVars = ['CI', 'CONTINUOUS_INTEGRATION', 'BUILD_NUMBER', 'JENKINS_URL',
            'TRAVIS', 'CIRCLECI', 'GITHUB_ACTIONS', 'GITLAB_CI'];
        const isCI = ciVars.some(varName => env[varName]);
        // Check for display availability
        const hasDisplay = !!(env.DISPLAY || env.WAYLAND_DISPLAY || process.platform === 'win32' || process.platform === 'darwin');
        // Headless detection
        const isHeadless = isCI || isDocker || !hasDisplay ||
            env.TERM === 'dumb' || env.DEBIAN_FRONTEND === 'noninteractive';
        this.environmentInfo = {
            isSSH,
            isDocker,
            isCI,
            isHeadless,
            hasDisplay
        };
        return this.environmentInfo;
    }
    /**
     * Test if a command/terminal is available in the system
     * Alias for testTerminalAvailability for backward compatibility
     */
    static async isCommandAvailable(command) {
        return await this.testTerminalAvailability(command);
    }
    /**
     * Test if a macOS application exists in /Applications/
     */
    static testMacOSApplication(appName) {
        const possiblePaths = [
            `/Applications/${appName}`,
            `/Applications/Utilities/${appName}`,
            `/System/Applications/${appName}`,
            `/System/Applications/Utilities/${appName}`
        ];
        return possiblePaths.some(appPath => {
            try {
                return fs.existsSync(appPath);
            }
            catch (error) {
                console.debug(`Error checking macOS app path ${appPath}:`, error);
                return false;
            }
        });
    }
    /**
     * Test if Windows Terminal is available via registry or PATH
     */
    static async testWindowsTerminal(command) {
        // For Windows Terminal specifically, check if it's installed via Microsoft Store
        if (command === 'wt.exe') {
            try {
                // Check if Windows Terminal is available in PATH
                const result = await new Promise((resolve) => {
                    const testProcess = spawn('where', ['wt'], {
                        stdio: 'ignore',
                        timeout: WINDOWS_TERMINAL_TEST_TIMEOUT_MS
                    });
                    testProcess.on('exit', (code) => {
                        resolve(code === 0);
                    });
                    testProcess.on('error', () => {
                        resolve(false);
                    });
                    setTimeout(() => {
                        testProcess.kill();
                        resolve(false);
                    }, WINDOWS_TERMINAL_TEST_TIMEOUT_MS);
                });
                if (result)
                    return true;
            }
            catch (error) {
                console.debug('Error testing Windows Terminal via PATH:', error);
            }
            // Fallback: check common installation paths
            const commonPaths = [
                path.join(process.env.LOCALAPPDATA || '', 'Microsoft\\WindowsApps\\wt.exe'),
                path.join(process.env.PROGRAMFILES || '', 'WindowsApps\\Microsoft.WindowsTerminal*\\wt.exe')
            ];
            for (const testPath of commonPaths) {
                try {
                    if (testPath.includes('*')) {
                        // Handle wildcard paths
                        const baseDir = path.dirname(testPath);
                        const fileName = path.basename(testPath);
                        if (fs.existsSync(baseDir)) {
                            const entries = fs.readdirSync(baseDir);
                            const matchingDirs = entries.filter(entry => entry.startsWith('Microsoft.WindowsTerminal') &&
                                fs.existsSync(path.join(baseDir, entry, fileName.replace('*', ''))));
                            if (matchingDirs.length > 0)
                                return true;
                        }
                    }
                    else if (fs.existsSync(testPath)) {
                        return true;
                    }
                }
                catch (error) {
                    console.debug(`Error checking Windows Terminal path ${testPath}:`, error);
                }
            }
        }
        return false;
    }
    /**
     * Validate environment variable overrides
     */
    static validateEnvironmentOverride(envVar) {
        const value = process.env[envVar];
        if (!value)
            return null;
        try {
            // Check if the command exists in PATH or as an absolute path
            if (path.isAbsolute(value)) {
                return fs.existsSync(value) ? value : null;
            }
            else {
                // For relative commands, we'll let the generic test handle it
                return value;
            }
        }
        catch (error) {
            console.debug(`Error validating environment override ${envVar}=${value}:`, error);
            return null;
        }
    }
    /**
     * Test if a terminal is available and working with platform-specific checks
     */
    static async testTerminalAvailability(terminalName) {
        // Check cache first
        if (this.terminalCache.has(terminalName)) {
            return this.terminalCache.get(terminalName);
        }
        // Check for environment variable overrides first
        const envOverride = this.validateEnvironmentOverride('TERMINAL') ||
            this.validateEnvironmentOverride('TERM_PROGRAM');
        if (envOverride && envOverride === terminalName) {
            this.terminalCache.set(terminalName, true);
            return true;
        }
        const platformInfo = getPlatformInfo();
        try {
            let isAvailable = false;
            // Platform-specific checks
            if (platformInfo.platform === 'darwin') {
                // macOS: Check for applications in /Applications/
                if (terminalName === 'osascript') {
                    // For osascript-based terminals, check if the actual app exists
                    const terminalPrefs = this.TERMINAL_PREFERENCES.darwin;
                    const currentTerminal = terminalPrefs.find(t => t.command === terminalName);
                    if (currentTerminal?.name === 'Terminal.app') {
                        isAvailable = this.testMacOSApplication('Terminal.app');
                    }
                    else if (currentTerminal?.name === 'iTerm2') {
                        isAvailable = this.testMacOSApplication('iTerm.app') || this.testMacOSApplication('iTerm2.app');
                    }
                    else {
                        // Fallback to generic test for osascript
                        isAvailable = await this.genericTerminalTest(terminalName);
                    }
                }
                else {
                    // For other commands, use generic test
                    isAvailable = await this.genericTerminalTest(terminalName);
                }
            }
            else if (platformInfo.platform === 'win32') {
                // Windows: Special handling for Windows Terminal and other terminals
                if (terminalName === 'wt.exe') {
                    isAvailable = await this.testWindowsTerminal(terminalName);
                }
                else {
                    // For other Windows terminals, use generic test
                    isAvailable = await this.genericTerminalTest(terminalName);
                }
            }
            else {
                // Linux and other platforms: use generic test
                isAvailable = await this.genericTerminalTest(terminalName);
            }
            this.terminalCache.set(terminalName, isAvailable);
            return isAvailable;
        }
        catch (error) {
            console.debug(`Terminal availability test failed for ${terminalName}:`, error);
            this.terminalCache.set(terminalName, false);
            return false;
        }
    }
    /**
     * Generic terminal test using spawn
     */
    static async genericTerminalTest(terminalName) {
        try {
            const result = await RetryManager.executeWithRetry(async () => {
                return new Promise((resolve, reject) => {
                    const testProcess = spawn(terminalName, ['--version'], {
                        stdio: 'ignore',
                        timeout: TERMINAL_TEST_TIMEOUT_MS
                    });
                    testProcess.on('spawn', () => {
                        testProcess.kill();
                        resolve(true);
                    });
                    testProcess.on('error', (error) => {
                        reject(error);
                    });
                    testProcess.on('exit', (code) => {
                        // Some terminals exit with 0 even for --version
                        resolve(code === 0 || code === null);
                    });
                    setTimeout(() => {
                        testProcess.kill();
                        reject(new Error('Test timeout'));
                    }, TERMINAL_TEST_TIMEOUT_MS);
                });
            }, RETRY_POLICIES.FILE_OPERATION_RETRY_POLICY);
            return result.success && result.result === true;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Find the best working terminal from a list
     */
    static async findWorkingTerminal(terminalList) {
        // Sort by priority (highest first)
        const sortedTerminals = [...terminalList].sort((a, b) => b.priority - a.priority);
        for (const terminal of sortedTerminals) {
            try {
                const isAvailable = await this.testTerminalAvailability(terminal.command);
                if (isAvailable) {
                    return {
                        ...terminal,
                        available: true,
                        tested: true
                    };
                }
            }
            catch (error) {
                console.debug(`Terminal ${terminal.name} test failed:`, error);
            }
        }
        return null;
    }
    /**
     * Get available terminals for current platform
     */
    static async getAvailableTerminals() {
        const platformInfo = getPlatformInfo();
        const platformTerminals = this.TERMINAL_PREFERENCES[platformInfo.platform] || [];
        const results = [];
        for (const terminal of platformTerminals) {
            const available = await this.testTerminalAvailability(terminal.command);
            results.push({
                ...terminal,
                available,
                tested: true
            });
        }
        return results;
    }
    /**
     * Find the best terminal for current environment
     */
    static async findBestTerminal() {
        const environment = this.detectEnvironment();
        // In headless environments, don't try to find GUI terminals
        if (environment.isHeadless || environment.isCI) {
            console.debug('Headless environment detected, skipping terminal detection');
            return null;
        }
        const platformInfo = getPlatformInfo();
        const platformTerminals = this.TERMINAL_PREFERENCES[platformInfo.platform] || [];
        // Apply environment-specific filtering
        let filteredTerminals = [...platformTerminals];
        if (environment.isSSH) {
            // In SSH sessions, prefer terminals that work well remotely
            filteredTerminals = filteredTerminals.filter(t => !t.name.includes('iTerm') && !t.name.includes('Terminal.app'));
        }
        if (environment.isDocker) {
            // In Docker, prefer simple terminals
            filteredTerminals = filteredTerminals.filter(t => t.name.includes('xterm') || t.name.includes('cmd') || t.name.includes('bash'));
        }
        // Convert terminal preferences to TerminalInfo objects
        const terminalInfos = filteredTerminals.map(t => ({
            name: t.name,
            command: t.command,
            priority: t.priority,
            available: false, // Will be tested by findWorkingTerminal
            tested: false
        }));
        return await this.findWorkingTerminal(terminalInfos);
    }
    /**
     * Check if terminal spawning is possible in current environment
     */
    static async canSpawnTerminal() {
        const environment = this.detectEnvironment();
        // Quick environment checks
        if (environment.isHeadless || environment.isCI) {
            return false;
        }
        // Try to find any working terminal
        const bestTerminal = await this.findBestTerminal();
        return bestTerminal !== null;
    }
    /**
     * Get fallback strategy for current environment
     */
    static getFallbackStrategy() {
        const environment = this.detectEnvironment();
        if (environment.isHeadless || environment.isCI) {
            return 'console';
        }
        if (environment.isSSH && !environment.hasDisplay) {
            return 'console';
        }
        return 'terminal';
    }
    /**
     * Clear terminal availability cache
     */
    static clearCache() {
        this.terminalCache.clear();
        this.environmentInfo = null;
    }
    /**
     * Get terminal preferences with validated environment overrides
     */
    static getTerminalPreferences() {
        // Check for environment variable overrides and validate them
        const envTerminal = this.validateEnvironmentOverride('TERMINAL') ||
            this.validateEnvironmentOverride('TERM_PROGRAM');
        if (envTerminal) {
            console.debug(`Using environment terminal override: ${envTerminal}`);
            return [envTerminal];
        }
        const platformInfo = getPlatformInfo();
        const platformTerminals = this.TERMINAL_PREFERENCES[platformInfo.platform] || [];
        return [...platformTerminals]
            .sort((a, b) => b.priority - a.priority)
            .map(t => t.command);
    }
}
//# sourceMappingURL=terminal-fallbacks.js.map