{"time":"2025-08-02T20:04:10.5957908+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":113},"msg":"Getting live provider data"}
{"time":"2025-08-02T20:04:10.9190333+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-02T20:04:10.9228996+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/config.Load","file":"/home/<USER>/work/crush/crush/internal/config/load.go","line":82},"msg":"No providers configured"}
{"time":"2025-08-02T20:04:13.2222782+02:00","level":"INFO","msg":"OK   20250424200609_initial.sql (7.86ms)"}
{"time":"2025-08-02T20:04:13.2252862+02:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (2.35ms)"}
{"time":"2025-08-02T20:04:13.2293829+02:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (4.1ms)"}
{"time":"2025-08-02T20:04:13.2325359+02:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (3.15ms)"}
{"time":"2025-08-02T20:04:13.2325359+02:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-02T20:04:13.2332148+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-02T20:04:13.2332148+02:00","level":"WARN","source":{"function":"github.com/charmbracelet/crush/internal/app.New","file":"/home/<USER>/work/crush/crush/internal/app/app.go","line":97},"msg":"No agent configuration found"}
{"time":"2025-08-02T20:05:34.0444837+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-02T20:05:34.0444837+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-02T20:05:34.0752393+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-02T20:05:34.0757511+02:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
