import { ChildProcess } from 'child_process';
/**
 * Information about a tracked process
 */
export interface ProcessInfo {
    pid: number;
    process: ChildProcess;
    spawnTime: Date;
    lastHeartbeat: Date;
    sessionId?: string;
}
/**
 * Robust orphan process tracking and cleanup system
 */
export declare class OrphanManager {
    private static instance;
    private readonly processes;
    private readonly maxTrackedProcesses;
    private staleCheckInterval;
    private signalHandlersRegistered;
    private constructor();
    /**
     * Get singleton instance of OrphanManager
     */
    static getInstance(): OrphanManager;
    /**
     * Register a process for tracking
     */
    registerProcess(process: ChildProcess, sessionId?: string): Promise<void>;
    /**
     * Remove a process from tracking
     */
    unregisterProcess(pid: number): void;
    /**
     * Update heartbeat timestamp for a process
     */
    updateHeartbeat(pid: number): void;
    /**
     * Check if a process is still alive
     */
    checkProcessHealth(pid: number): boolean;
    /**
     * Find processes that haven't had activity within the timeout
     */
    findStaleProcesses(timeoutMs: number): ProcessInfo[];
    /**
     * Kill a specific process gracefully then forcefully
     */
    killProcess(pid: number, signal?: NodeJS.Signals): Promise<boolean>;
    /**
     * Kill all tracked processes
     */
    killAllProcesses(): Promise<void>;
    /**
     * Clean up stale processes
     */
    killStaleProcesses(timeoutMs: number): Promise<number>;
    /**
     * Get information about all tracked processes
     */
    getTrackedProcesses(): ProcessInfo[];
    /**
     * Get process count
     */
    getProcessCount(): number;
    /**
     * Check if running on Windows
     */
    private isWindows;
    /**
     * Kill process on Windows using taskkill
     */
    private killWindowsProcess;
    /**
     * Simple delay utility
     */
    private delay;
    /**
     * Register signal handlers for cleanup
     */
    private registerSignalHandlers;
    /**
     * Start monitoring for stale processes
     */
    private startStaleProcessMonitoring;
    /**
     * Stop monitoring for stale processes
     */
    stopStaleProcessMonitoring(): void;
}
export declare const orphanManager: OrphanManager;
//# sourceMappingURL=orphan-manager.d.ts.map