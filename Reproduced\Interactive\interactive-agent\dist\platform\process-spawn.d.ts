import { ChildProcess } from 'child_process';
import { RetryPolicy } from '../utils/retry-manager.js';
export interface SpawnOptions {
    cwd?: string;
    env?: Record<string, string>;
    detached?: boolean;
    windowTitle?: string;
    args?: string[];
    retryPolicy?: RetryPolicy;
    spawnTimeout?: number;
    abortSignal?: AbortSignal;
    sessionId?: string;
}
export interface SpawnResult {
    process: ChildProcess | null;
    success: boolean;
    error?: string;
    sessionId?: string;
    retryCount?: number;
    lastError?: Error;
}
/**
 * Spawn un processus dans un nouveau terminal selon la plateforme
 */
export declare function spawnInTerminal(command: string, args?: string[], options?: SpawnOptions): SpawnResult;
/**
 * Teste si le spawn de terminal fonctionne sur cette plateforme
 */
export declare function testTerminalSpawn(): Promise<boolean>;
/**
 * Enhanced spawn function with retry logic, timeout handling, and orphan tracking
 */
export declare function spawnWithRetry(command: string, args?: string[], options?: SpawnOptions): Promise<SpawnResult>;
//# sourceMappingURL=process-spawn.d.ts.map