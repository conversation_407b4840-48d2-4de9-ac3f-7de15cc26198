# Interactive Agent

[![npm version](https://badge.fury.io/js/interactive-agent.svg)](https://badge.fury.io/js/interactive-agent)
[![Node.js CI](https://github.com/GiGiDKR/interactive-agent/workflows/Node.js%20CI/badge.svg)](https://github.com/GiGiDKR/interactive-agent/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A minimal, optimized interactive agent for Model Context Protocol (MCP) with robust platform detection and intelligent process management. This is a streamlined version of interactive-mcp, designed for performance and minimal dependencies.

## Features

- 🚀 **Robust Platform Detection**: Intelligent detection of Windows, macOS, Linux, WSL, and MSYS environments
- 🔄 **Intelligent Retry Mechanisms**: Exponential backoff with jitter for reliable operation
- 🧹 **Orphan Process Management**: Automatic cleanup of spawned processes on exit
- 📁 **Temporary File Cleanup**: Automatic cleanup with FinalizationRegistry support
- ⚡ **Minimal Dependencies**: Optimized for performance with essential dependencies only
- 🔧 **Cross-Platform Terminal Support**: Seamless terminal spawning across different platforms
- 🎯 **TypeScript First**: Full TypeScript support with comprehensive type definitions

## Installation

### For End Users

```bash
npm install -g interactive-agent
```

### For Developers

```bash
git clone https://github.com/GiGiDKR/interactive-agent.git
cd interactive-agent
npm install
npm run build
```

## Configuration

### Claude Desktop

Add to your Claude Desktop configuration file:

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Linux**: `~/.config/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "interactive-agent": {
      "command": "interactive-agent",
      "args": []
    }
  }
}
```

### Cursor

Add to your Cursor settings:

```json
{
  "mcp.servers": {
    "interactive-agent": {
      "command": "interactive-agent",
      "args": []
    }
  }
}
```

### VS Code

Install the MCP extension and add to your settings:

```json
{
  "mcp.servers": {
    "interactive-agent": {
      "command": "interactive-agent",
      "args": []
    }
  }
}
```

## Platform-Specific Setup

### Windows

The agent automatically detects and supports:
- **Command Prompt** (`cmd.exe`)
- **PowerShell** (both Windows PowerShell and PowerShell Core)
- **Windows Terminal** (if installed)
- **WSL** environments (Ubuntu, Debian, etc.)
- **MSYS2/MinGW** environments

### macOS

Supports all standard macOS terminals:
- **Terminal.app** (default)
- **iTerm2** (if installed)
- **Hyper** (if installed)
- **Alacritty** (if installed)

### Linux

Comprehensive Linux terminal support:
- **GNOME Terminal** (default on GNOME)
- **Konsole** (default on KDE)
- **xterm** (fallback)
- **Alacritty**, **Kitty**, **Terminator** (if installed)
- **Headless environments** (SSH, Docker, CI/CD)

## Development Setup

### Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn
- Git

### Installation

```bash
# Clone the repository
git clone https://github.com/GiGiDKR/interactive-agent.git
cd interactive-agent

# Install dependencies
npm install

# Build the project
npm run build

# Run tests
npm test

# Run platform validation
npm run platform:validate
```

### Development Scripts

```bash
# Development build with source maps
npm run build:dev

# Production build with optimizations
npm run build:prod

# Watch mode for development
npm run dev

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run platform-specific tests
npm run test:platform

# Validate entire project
npm run validate

# Clean build artifacts
npm run clean
```

## API Reference

### Platform Detection

```typescript
import { getPlatformInfo } from 'interactive-agent';

const platformInfo = await getPlatformInfo();
console.log(platformInfo);
// {
//   platform: 'win32' | 'darwin' | 'linux',
//   isWSL: boolean,
//   isMSYS: boolean,
//   shell: string,
//   hasDisplay: boolean,
//   isHeadless: boolean,
//   terminals: string[]
// }
```

### Process Spawning

```typescript
import { spawnInTerminal } from 'interactive-agent';

const result = await spawnInTerminal('echo "Hello World"', {
  cwd: process.cwd(),
  timeout: 30000
});
```

### Temporary File Management

```typescript
import { createTempFile, createTempDir } from 'interactive-agent';

const tempFile = await createTempFile('content', '.txt');
const tempDir = await createTempDir();
```

## Troubleshooting

### Common Issues

**Terminal not found on Linux**
```bash
# Install a supported terminal
sudo apt install gnome-terminal  # Ubuntu/Debian
sudo yum install gnome-terminal  # RHEL/CentOS
```

**Permission denied on macOS**
```bash
# Grant terminal permissions in System Preferences > Security & Privacy
```

**WSL detection issues**
```bash
# Ensure WSL is properly configured
wsl --list --verbose
```

### Performance Notes

- Platform detection is cached after first call
- Process spawning uses intelligent retry with exponential backoff
- Temporary files are automatically cleaned up using FinalizationRegistry
- Minimal dependency footprint for faster startup times

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and add tests
4. Run the validation suite: `npm run validate`
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

### Development Workflow

```bash
# Run tests in watch mode during development
npm run test:watch

# Validate platform compatibility
npm run platform:validate

# Check TypeScript types
npm run type-check

# Build and test everything
npm run validate
```

## License

MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for version history and updates.
