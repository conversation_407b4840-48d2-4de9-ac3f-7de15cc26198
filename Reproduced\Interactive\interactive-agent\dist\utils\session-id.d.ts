/**
 * Generate a cryptographically secure session ID
 * Uses crypto.randomBytes for security and consistency across the application
 *
 * @param byteLength - Optional length in bytes (default from constants)
 * @returns A hexadecimal session ID string (16 hex chars = 8 bytes)
 */
export declare function generateSessionId(byteLength?: number): string;
/**
 * Generate a session ID with a specific prefix
 * Useful for creating identifiable session IDs for different purposes
 *
 * @param prefix - Prefix to add to the session ID
 * @param length - Optional length in bytes for the random part
 * @returns A prefixed session ID string
 */
export declare function generatePrefixedSessionId(prefix: string, length?: number): string;
/**
 * Validate if a string looks like a valid session ID
 * Checks if it's a hexadecimal string of expected length
 *
 * @param sessionId - The session ID to validate
 * @returns True if the session ID appears valid
 */
export declare function isValidSessionId(sessionId: string): boolean;
/**
 * Extract the session ID part from a prefixed session ID
 *
 * @param prefixedSessionId - A session ID that may have a prefix
 * @returns The session ID part without prefix
 */
export declare function extractSessionId(prefixedSessionId: string): string;
//# sourceMappingURL=session-id.d.ts.map