import { ChildProcess } from 'child_process';
import { STALE_PROCESS_CHECK_INTERVAL_MS, STALE_PROCESS_TIMEOUT_MS, PROCESS_KILL_TIMEOUT_MS } from '../constants.js';

/**
 * Information about a tracked process
 */
export interface ProcessInfo {
  pid: number;
  process: ChildProcess;
  spawnTime: Date;
  lastHeartbeat: Date;
  sessionId?: string;
}

/**
 * Robust orphan process tracking and cleanup system
 */
export class OrphanManager {
  private static instance: OrphanManager | null = null;
  private readonly processes = new Map<number, ProcessInfo>();
  private readonly maxTrackedProcesses = 100;
  private staleCheckInterval: NodeJS.Timeout | null = null;
  private signalHandlersRegistered = false;
  private taskkillAvailable: boolean | null = null; // Cache taskkill availability

  private constructor() {
    this.registerSignalHandlers();
    this.startStaleProcessMonitoring();
  }

  /**
   * Get singleton instance of OrphanManager
   */
  public static getInstance(): OrphanManager {
    if (!OrphanManager.instance) {
      OrphanManager.instance = new OrphanManager();
    }
    return OrphanManager.instance;
  }

  /**
   * Register a process for tracking
   */
  public async registerProcess(process: ChildProcess, sessionId?: string): Promise<void> {
    if (!process.pid) {
      console.warn('Cannot register process without PID');
      return;
    }

    // Enforce process limit
    if (this.processes.size >= this.maxTrackedProcesses) {
      console.warn(`Process registry full (${this.maxTrackedProcesses}), cleaning stale processes`);
      await this.killStaleProcesses(STALE_PROCESS_TIMEOUT_MS);
    }

    const processInfo: ProcessInfo = {
      pid: process.pid,
      process,
      spawnTime: new Date(),
      lastHeartbeat: new Date(),
      sessionId
    };

    this.processes.set(process.pid, processInfo);

    // Set up automatic cleanup when process exits normally
    process.on('exit', () => {
      this.unregisterProcess(process.pid!);
    });

    console.debug(`Registered process ${process.pid} for tracking`, { sessionId });
  }

  /**
   * Remove a process from tracking
   */
  public unregisterProcess(pid: number): void {
    if (this.processes.delete(pid)) {
      console.debug(`Unregistered process ${pid} from tracking`);
    }
  }

  /**
   * Update heartbeat timestamp for a process
   */
  public updateHeartbeat(pid: number): void {
    const processInfo = this.processes.get(pid);
    if (processInfo) {
      processInfo.lastHeartbeat = new Date();
    }
  }

  /**
   * Check if a process is still alive
   */
  public checkProcessHealth(pid: number): boolean {
    try {
      // Sending signal 0 checks if process exists without actually sending a signal
      process.kill(pid, 0);
      return true;
    } catch (error) {
      // Process doesn't exist or we don't have permission
      return false;
    }
  }

  /**
   * Find processes that haven't had activity within the timeout
   */
  public findStaleProcesses(timeoutMs: number): ProcessInfo[] {
    const now = new Date();
    const staleProcesses: ProcessInfo[] = [];

    for (const processInfo of this.processes.values()) {
      const timeSinceHeartbeat = now.getTime() - processInfo.lastHeartbeat.getTime();
      
      if (timeSinceHeartbeat > timeoutMs) {
        // Double-check if process is actually dead
        if (!this.checkProcessHealth(processInfo.pid)) {
          staleProcesses.push(processInfo);
        } else {
          // Process is alive, update heartbeat
          this.updateHeartbeat(processInfo.pid);
        }
      }
    }

    return staleProcesses;
  }

  /**
   * Kill a specific process gracefully then forcefully
   */
  public async killProcess(pid: number, signal: NodeJS.Signals = 'SIGTERM'): Promise<boolean> {
    const processInfo = this.processes.get(pid);
    if (!processInfo) {
      console.warn(`Process ${pid} not found in registry`);
      return false;
    }

    try {
      console.debug(`Attempting to kill process ${pid} with ${signal}`);
      
      // Try graceful termination first
      if (this.isWindows()) {
        // Windows doesn't support SIGTERM, use taskkill
        await this.killWindowsProcess(pid);
      } else {
        // POSIX systems
        process.kill(pid, signal);
        
        // Wait a bit for graceful shutdown
        await this.delay(PROCESS_KILL_TIMEOUT_MS);
        
        // If still alive, force kill
        if (this.checkProcessHealth(pid)) {
          console.debug(`Process ${pid} still alive, force killing`);
          process.kill(pid, 'SIGKILL');
        }
      }

      // Remove from tracking
      this.unregisterProcess(pid);
      return true;
    } catch (error) {
      console.warn(`Failed to kill process ${pid}:`, error);
      // Still remove from tracking since we tried
      this.unregisterProcess(pid);
      return false;
    }
  }

  /**
   * Kill all tracked processes
   */
  public async killAllProcesses(): Promise<void> {
    console.debug(`Killing all ${this.processes.size} tracked processes`);
    
    const killPromises: Promise<boolean>[] = [];
    for (const processInfo of this.processes.values()) {
      killPromises.push(this.killProcess(processInfo.pid));
    }

    await Promise.allSettled(killPromises);
    this.processes.clear();
  }

  /**
   * Clean up stale processes
   */
  public async killStaleProcesses(timeoutMs: number): Promise<number> {
    const staleProcesses = this.findStaleProcesses(timeoutMs);
    
    if (staleProcesses.length > 0) {
      console.debug(`Found ${staleProcesses.length} stale processes to clean up`);
      
      const killPromises = staleProcesses.map(processInfo => 
        this.killProcess(processInfo.pid)
      );
      
      await Promise.allSettled(killPromises);
    }

    return staleProcesses.length;
  }

  /**
   * Get information about all tracked processes
   */
  public getTrackedProcesses(): ProcessInfo[] {
    return Array.from(this.processes.values());
  }

  /**
   * Get process count
   */
  public getProcessCount(): number {
    return this.processes.size;
  }

  /**
   * Check if running on Windows
   */
  private isWindows(): boolean {
    return process.platform === 'win32';
  }

  /**
   * Check if taskkill is available on Windows
   */
  private async checkTaskkillAvailability(): Promise<boolean> {
    if (this.taskkillAvailable !== null) {
      return this.taskkillAvailable;
    }

    if (!this.isWindows()) {
      this.taskkillAvailable = false;
      return false;
    }

    try {
      const { spawn } = await import('child_process');

      return new Promise<boolean>((resolve) => {
        const test = spawn('taskkill', ['/?'], { stdio: 'ignore' });

        test.on('exit', (code) => {
          this.taskkillAvailable = code === 0;
          resolve(this.taskkillAvailable);
        });

        test.on('error', () => {
          this.taskkillAvailable = false;
          resolve(false);
        });

        // Timeout after 2 seconds
        setTimeout(() => {
          test.kill();
          this.taskkillAvailable = false;
          resolve(false);
        }, 2000);
      });
    } catch (error) {
      this.taskkillAvailable = false;
      return false;
    }
  }

  /**
   * Kill process on Windows using taskkill with fallback
   */
  private async killWindowsProcess(pid: number): Promise<void> {
    // Check if taskkill is available
    const taskkillAvailable = await this.checkTaskkillAvailability();

    if (taskkillAvailable) {
      try {
        await this.killWithTaskkill(pid);
        return;
      } catch (error) {
        console.warn(`taskkill failed for PID ${pid}, trying fallback:`, error);
      }
    }

    // Fallback: try using Node.js process.kill with supported signals
    try {
      // On Windows, only 'SIGKILL' and 'SIGTERM' are somewhat supported
      // but SIGTERM doesn't actually terminate, so we use SIGKILL
      process.kill(pid, 'SIGKILL');
    } catch (error) {
      throw new Error(`Failed to kill Windows process ${pid}: ${error}`);
    }
  }

  /**
   * Kill process using taskkill command
   */
  private async killWithTaskkill(pid: number): Promise<void> {
    const { spawn } = await import('child_process');

    return new Promise((resolve, reject) => {
      const taskkill = spawn('taskkill', ['/F', '/PID', pid.toString()]);

      taskkill.on('exit', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`taskkill failed with code ${code}`));
        }
      });

      taskkill.on('error', reject);

      // Add timeout to prevent hanging
      setTimeout(() => {
        taskkill.kill();
        reject(new Error('taskkill timeout'));
      }, 5000);
    });
  }

  /**
   * Simple delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Register signal handlers for cleanup
   */
  private registerSignalHandlers(): void {
    if (this.signalHandlersRegistered) {
      return;
    }

    const cleanup = async () => {
      console.debug('OrphanManager: Cleaning up processes on exit');
      await this.killAllProcesses();
    };

    // Handle various exit scenarios with timeouts
    let isExiting = false;

    const synchronousCleanup = () => {
      if (isExiting) return;
      isExiting = true;

      // Synchronous cleanup on exit - only essential operations
      for (const processInfo of this.processes.values()) {
        try {
          if (this.isWindows()) {
            // On Windows, SIGTERM doesn't work properly, use SIGKILL
            // Note: This is synchronous and limited, but it's the best we can do on exit
            process.kill(processInfo.pid, 'SIGKILL');
          } else {
            process.kill(processInfo.pid, 'SIGKILL');
          }
        } catch (error) {
          // Silently ignore errors during exit cleanup
          // Process might already be dead or we might not have permission
        }
      }

      // Clear interval synchronously
      if (this.staleCheckInterval) {
        clearInterval(this.staleCheckInterval);
        this.staleCheckInterval = null;
      }
    };

    process.on('exit', synchronousCleanup);

    const gracefulShutdown = (signal: string) => {
      if (isExiting) return;

      console.debug(`OrphanManager: Received ${signal}, initiating graceful shutdown`);

      // Set a timeout to prevent hanging
      const shutdownTimeout = setTimeout(() => {
        console.warn('OrphanManager: Shutdown timeout reached, forcing exit');
        synchronousCleanup();
        process.exit(1);
      }, PROCESS_KILL_TIMEOUT_MS);

      // Attempt graceful cleanup with timeout
      cleanup()
        .then(() => {
          clearTimeout(shutdownTimeout);
          process.exit(0);
        })
        .catch((error) => {
          console.warn('OrphanManager: Error during graceful shutdown:', error);
          clearTimeout(shutdownTimeout);
          synchronousCleanup();
          process.exit(1);
        });
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

    // Windows specific
    if (this.isWindows()) {
      process.on('SIGBREAK', async () => {
        await cleanup();
        process.exit(0);
      });
    }

    this.signalHandlersRegistered = true;
  }

  /**
   * Start monitoring for stale processes
   */
  private startStaleProcessMonitoring(): void {
    if (this.staleCheckInterval) {
      return;
    }

    this.staleCheckInterval = setInterval(async () => {
      try {
        await this.killStaleProcesses(STALE_PROCESS_TIMEOUT_MS);
      } catch (error) {
        console.warn('Error during stale process cleanup:', error);
      }
    }, STALE_PROCESS_CHECK_INTERVAL_MS);
  }

  /**
   * Stop monitoring for stale processes
   */
  public stopStaleProcessMonitoring(): void {
    if (this.staleCheckInterval) {
      clearInterval(this.staleCheckInterval);
      this.staleCheckInterval = null;
      console.debug('OrphanManager: Stopped stale process monitoring');
    }
  }
}

// Export singleton instance
export const orphanManager = OrphanManager.getInstance();
