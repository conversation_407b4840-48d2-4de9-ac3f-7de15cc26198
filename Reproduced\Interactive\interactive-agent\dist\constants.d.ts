/**
 * Essential constants for interactive-agent
 * Minimal version based on interactive-mcp constants
 */
export declare const USER_INPUT_TIMEOUT_SECONDS: 60;
export declare const TERMINAL_SPAWN_TIMEOUT_MS: 5000;
export declare const MAX_SPAWN_RETRIES: 3;
export declare const SPAWN_RETRY_DELAY_MS: 1000;
export declare const PLATFORM_CACHE_TTL_MS: 300000;
export declare const SPAWN_START_TIMEOUT_MS: 5000;
export declare const HEARTBEAT_TIMEOUT_MS: 10000;
export declare const HEARTBEAT_INTERVAL_MS: 2000;
export declare const PROCESS_KILL_TIMEOUT_MS: 3000;
export declare const TERMINAL_TEST_TIMEOUT_MS: 3000;
export declare const WINDOWS_TERMINAL_TEST_TIMEOUT_MS: 2000;
export declare const STALE_PROCESS_CHECK_INTERVAL_MS: 30000;
export declare const STALE_PROCESS_TIMEOUT_MS: 30000;
export declare const INPUT_RESPONSE_CHECK_INTERVAL_MS: 500;
export declare const DEFAULT_MAX_RETRIES: 3;
export declare const DEFAULT_RETRY_BASE_DELAY_MS: 1000;
export declare const DEFAULT_RETRY_MAX_DELAY_MS: 10000;
export declare const DEFAULT_BACKOFF_FACTOR: 2;
export declare const TEMP_FILE_PREFIX: "interactive-agent";
export declare const SESSION_ID_BYTE_LENGTH: 8;
export declare const SESSION_ID_HEX_LENGTH: 16;
export declare const MAX_TRACKED_PROCESSES: 100;
export declare const DEFAULT_SHELL_PATHS: {
    readonly windows: readonly ["cmd.exe", "powershell.exe"];
    readonly unix: readonly ["/bin/bash", "/bin/sh", "/bin/zsh", "/bin/fish"];
};
export declare const TERMINAL_PREFERENCES: {
    readonly win32: readonly ["wt.exe", "ConEmu64.exe", "ConEmu.exe", "cmd.exe"];
    readonly darwin: readonly ["Terminal.app", "iTerm.app"];
    readonly linux: readonly ["gnome-terminal", "konsole", "xfce4-terminal", "mate-terminal", "xterm", "urxvt"];
};
export type PlatformType = 'win32' | 'darwin' | 'linux' | 'other';
//# sourceMappingURL=constants.d.ts.map