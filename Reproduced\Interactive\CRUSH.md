## Project Commands

- **Build:** `pnpm build`
- **Lint:** `pnpm lint`
- **Format:** `pnpm format`
- **Test:** `pnpm test` (in `interactive-agent` directory)
- **Run a single test file:** `pnpm test -- <file_path>`

## Code Style

- **Formatting:** Code is formatted with Prettier. Run `pnpm format` to format all files.
- **Imports:** Use ES module imports. Aliases are configured for `@/*` to `src/*`.
- **Types:** Use TypeScript with strict mode enabled.
- **Naming Conventions:** Follow standard TypeScript naming conventions (e.g., `camelCase` for variables and functions, `PascalCase` for classes and types).
- **Error Handling:** No specific error handling conventions found.
- **Linting:** ESLint is used for linting. Run `pnpm lint` to check for issues.
- **Dependencies:** Use `pnpm` for package management.

## Communication

This project uses `request_user_input` to communicate with the user. Please use this tool to ask questions and confirm actions.

- **Prioritize Interaction:** Utilize `request_user_input` frequently to engage with the user.
- **Seek Clarification:** If requirements, instructions, or context are unclear, **always** ask clarifying questions before proceeding. Do not make assumptions.
- **Confirm Actions:** Before performing significant actions (like modifying files, running complex commands, or making architectural decisions), confirm the plan with the user.
- **Provide Options:** Whenever possible, present the user with predefined options to facilitate quick decisions.
